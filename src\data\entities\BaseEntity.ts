import { Info } from '../FieldTypes'

export interface BaseEntity {
  id?: string
  createAt?: Date
  createBy?: string
  updateAt?: Date
  updateBy?: string
}

export const BaseEntityInfo: Info<BaseEntity> = {
  typeName: 'BaseEntity',
  nameKey: 'id',
  sortKey: 'id',
  backend: 'BaseBiz',
  endpoint: 'Dummy',
  fields: {
    id: { label: 'ID', type: 'smalltext' },
    createAt: { label: 'Created At', type: 'datetime' },
    createBy: { label: 'Created By', type: 'smalltext' },
    updateAt: { label: 'Updated At', type: 'datetime' },
    updateBy: { label: 'Updated By', type: 'smalltext' },
  },
  options: {},
  default: {},
  columnsShown: new Set(),
  formLayout: [['id'], ['createAt', 'createBy'], ['updateAt', 'updateBy']],
}

<script setup lang="ts">
import { MainType, SubType } from '../../../../data/entities/Project'

const mainBadgeColor = () => {
  return props.type === '01' ? 'danger' : 'warning'
}
const subBadgeColor = () => {
  return props.type === 'Agile' ? 'primary' : 'secondary'
}

const props = defineProps<{
  level: 'main' | 'sub'
  type: MainType | SubType | undefined | null
}>()
</script>

<template>
  <VaBadge
    v-if="props.type"
    class="m-0.5"
    square
    :color="level === 'main' ? mainBadgeColor() : subBadgeColor()"
    :text="props.type!.toUpperCase()"
  />
</template>

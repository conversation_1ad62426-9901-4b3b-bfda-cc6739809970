export const toUtc = (date: any) => {
  return new Date((date as string) + 'Z')
}

const minutesWorkedInDay = (ms: number) => Math.max(Math.min(Math.floor(ms / 1000 / 60 - 9 * 60), 8 * 60), 0)

export const calcMinutesWorked = (start: Date, end: Date) => {
  const startTime = start.getTime()
  const endTime = end.getTime()
  const startDateTime = new Date(start).setHours(0, 0, 0, 0)
  const endDateTime = new Date(end).setHours(0, 0, 0, 0)
  const msDay = 24 * 60 * 60 * 1000
  let minutes = 0
  // we don't have enough info to count overtime minutes if you start before 9am or work for more than 8 hours
  minutes -= minutesWorkedInDay(startTime - startDateTime)
  minutes += minutesWorkedInDay(endTime - endDateTime)

  const diffDays = Math.floor((endDateTime - startDateTime) / msDay)
  const diffWeeks = Math.floor(diffDays / 7)
  minutes += diffWeeks * 40 * 60 // 40 hours per week
  let bufferDays = 0 // buffer days might be weekends
  for (let i = 0; i < diffDays % 7; i++) {
    const day = new Date(startDateTime + i * msDay).getDay()
    if (day !== 0 && day !== 6) {
      bufferDays++
    }
  }
  minutes += bufferDays * 8 * 60 // 8 hours per day
  return minutes
}

// #d #h #m (?: means non-capturing group)
export const timeRegex: RegExp = /-?(\d*)h?\s?(\d*)m?/
// convert minutes to #d #h #m
export const formatTime = (m?: number) => (!m ? undefined : Math.floor(m / 60) + 'h ' + ((m % 60) + 'm'))
const getNum = (s: string) => (s != '' ? parseInt(s) : 0)
export const parseTime = (s?: string) => {
  if (!s) {
    return undefined
  }
  console.log(s)
  if (!isNaN(Number(s))) {
    return parseInt(s)
  }
  const matches = timeRegex.exec(s)
  console.log(matches)
  if (!matches || matches.length === 0) {
    return 0
  }
  const negate = s.startsWith('-')
  return (negate ? -1 : 1) * (getNum(matches[1]) * 60 + getNum(matches[2]))
}

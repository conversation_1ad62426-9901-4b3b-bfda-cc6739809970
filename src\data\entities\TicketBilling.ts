import { codes } from '../CurrencyCodes'
import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { TicketBillingDelivery } from './TicketBillingDelivery'
import { TicketBillingPayment } from './TicketBillingPayment'

export interface TicketBilling extends TenantEntity {
  ticketId: string
  analysisDone: boolean
  requirementsDone: boolean
  specificationsDone: boolean
  billable: boolean
  estimateDone: boolean
  estimateAmount: number
  sowApproved: boolean
  sowFileId?: string
  unitType?: string
  rate: number
  lowUnit: number
  lowRate: number
  lowAmount: number
  highUnit: number
  highRate: number
  highAmount: number
  mostLikelyUnit: number
  mostLikelyRate: number
  mostLikelyAmount: number
  quotedType?: string
  quotedPrice: number
  currency?: string
  ticketBillingDeliveries?: TicketBillingDelivery[]
  ticketBillingPayments?: TicketBillingPayment[]
}

export const UnitTypes = ['Hour', 'Day', 'Week', 'Month', 'Fixed'] as const
export type UnitType = (typeof UnitTypes)[number]

export const QuotedTypes = ['Fixed', 'TimeAndMaterial', 'Estimate'] as const
export type QuotedType = (typeof QuotedTypes)[number]

export const ticketBillingInfo: Info<TicketBilling> = {
  typeName: 'Ticket Billing',
  nameKey: 'ticketId',
  sortKey: 'createAt',
  backend: 'Ticket',
  endpoint: 'TicketBilling',
  fields: {
    ticketId: { label: 'Ticket', type: 'external', required: true },
    analysisDone: { label: 'Analysis Done', type: 'bool', required: true },
    requirementsDone: { label: 'Requirements Done', type: 'bool', required: true },
    specificationsDone: { label: 'Specifications Done', type: 'bool', required: true },
    billable: { label: 'Billable', type: 'bool', required: true },
    estimateDone: { label: 'Estimate Done', type: 'bool', required: true },
    estimateAmount: { label: 'Estimate Amount', type: 'currency', required: true },
    sowApproved: { label: 'SOW Approved', type: 'bool', required: true },
    sowFileId: { label: 'SOW File', type: 'smalltext' },
    unitType: { label: 'Unit Type', type: 'smalltext' },
    rate: { label: 'Rate', type: 'currency', required: true },
    lowUnit: { label: 'Low Unit', type: 'smalltext', required: true },
    lowRate: { label: 'Low Rate', type: 'currency', required: true },
    lowAmount: { label: 'Low Amount', type: 'currency', required: true },
    highUnit: { label: 'High Unit', type: 'smalltext', required: true },
    highRate: { label: 'High Rate', type: 'currency', required: true },
    highAmount: { label: 'High Amount', type: 'currency', required: true },
    mostLikelyUnit: { label: 'Most Likely Unit', type: 'smalltext', required: true },
    mostLikelyRate: { label: 'Most Likely Rate', type: 'currency', required: true },
    mostLikelyAmount: { label: 'Most Likely Amount', type: 'currency', required: true },
    quotedType: { label: 'Quoted Type', type: 'smalltext' },
    quotedPrice: { label: 'Quoted Price', type: 'currency', required: true },
    currency: { label: 'Currency', type: 'select', required: true },
    ticketBillingDeliveries: { label: 'Deliveries', type: 'objlist' },
    ticketBillingPayments: { label: 'Payments', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    ticketId: { entity: 'Ticket' },
    currency: { options: Object.keys(codes) },
    // sowFileId: { entity: 'File' },
    ticketBillingDeliveries: { entity: 'TicketBillingDelivery' },
    ticketBillingPayments: { entity: 'TicketBillingPayment' },
    estimateAmount: { currencyKey: 'currency' },
    rate: { currencyKey: 'currency' },
    lowRate: { currencyKey: 'currency' },
    lowAmount: { currencyKey: 'currency' },
    highRate: { currencyKey: 'currency' },
    highAmount: { currencyKey: 'currency' },
    mostLikelyRate: { currencyKey: 'currency' },
    mostLikelyAmount: { currencyKey: 'currency' },
    quotedPrice: { currencyKey: 'currency' },
    ...TenantEntityInfo.options,
  },
  default: {
    ticketId: '',
    analysisDone: false,
    requirementsDone: false,
    specificationsDone: false,
    billable: false,
    estimateDone: false,
    estimateAmount: 0,
    sowApproved: false,
    unitType: 'Hour',
    rate: 0,
    lowUnit: 0,
    lowRate: 0,
    lowAmount: 0,
    highUnit: 0,
    highRate: 0,
    highAmount: 0,
    mostLikelyUnit: 0,
    mostLikelyRate: 0,
    mostLikelyAmount: 0,
    quotedPrice: 0,
    currency: 'CAD',
    ticketBillingDeliveries: [],
    ticketBillingPayments: [],
  },
  columnsShown: new Set(['ticketId', 'billable', 'quotedType', 'quotedPrice', 'estimateAmount']),
  formLayout: [
    ['ticketId'],
    ['analysisDone', 'requirementsDone'],
    ['specificationsDone', 'estimateDone'],
    ['estimateAmount', 'sowFileId'],
    ['unitType', 'mostLikelyUnit'],
    ['lowUnit', 'lowRate'],
    ['highUnit', 'highRate'],
    ['lowAmount', 'highAmount'],
    ['currency', 'rate'],
    ['mostLikelyRate', 'mostLikelyAmount'],
    ['quotedType', 'quotedPrice'],
    ['sowApproved', 'billable'],
  ],
}

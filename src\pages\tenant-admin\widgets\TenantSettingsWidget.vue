<template>
  <VaInput v-model="searchValue" class="mb-4" placeholder="Search Settings">
    <template #appendInner>
      <VaIcon name="mso-search" />
    </template>
  </VaInput>

  <section v-if="filteredCards.length" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
    <VaCard
      v-for="card in filteredCards"
      :key="card.id"
      :to="card.route"
      class="cursor-pointer hover:shadow-lg transition-shadow"
    >
      <VaCardContent>
        <VaIcon :name="`mso-${card.icon}`" size="2rem" color="primary" class="mb-2" />
        <h3 class="text-lg font-bold text-primary mb-1">{{ card.name }}</h3>
        <p>{{ card.intro }}</p>
      </VaCardContent>
    </VaCard>
  </section>

  <VaAlert v-else color="info" outline> No results found. </VaAlert>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { tenantSettings } from '../tenantsettings'

const searchValue = ref('')

const filteredCards = computed(() => {
  const term = searchValue.value.toLowerCase().trim()
  if (!term) return tenantSettings
  return tenantSettings.filter(
    (card) => card.name.toLowerCase().includes(term) || card.intro.toLowerCase().includes(term),
  )
})
</script>

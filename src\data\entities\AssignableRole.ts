import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { RoleAssignment } from './RoleAssignment'

export interface AssignableRole extends TenantEntity {
  organizationId: string
  roleId: string
  isInheritable: boolean
  roleAssignments?: RoleAssignment[]
}

export const assignableRoleInfo: Info<AssignableRole> = {
  typeName: 'Assignable Role',
  nameKey: 'roleId',
  sortKey: 'organizationId',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'AssignableRole',
  fields: {
    organizationId: { label: 'Organization', type: 'external', required: true },
    roleId: { label: 'Role', type: 'external', required: true },
    isInheritable: { label: 'Inheritable', type: 'bool', required: true },
    roleAssignments: { label: 'Role Assignments', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    organizationId: { entity: 'OrganizationHierarchy' },
    roleId: { entity: 'Role', constraints: { usage: ['Organization', 'Both'] } },
    ...TenantEntityInfo.options,
  },
  default: {
    organizationId: '',
    roleId: '',
    isInheritable: true,
  },
  columnsShown: new Set(['organizationId', 'roleId', 'isInheritable']),
  formLayout: [['organizationId', 'roleId'], ['isInheritable']],
}

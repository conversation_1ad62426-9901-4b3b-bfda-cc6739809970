import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { round } from '../../services/utils'
import { Invoice } from './Invoice'

export interface InvoiceItem extends TenantEntity {
  invoiceId: string
  description: string
  unit: number
  rate: number
  amount: number
  currency: string
}

export const invoiceItemInfo: Info<InvoiceItem> = {
  typeName: 'Invoice Item',
  typeShortName: 'Item',
  nameKey: 'description',
  sortAsc: true,
  sortKey: 'description',
  backend: 'Ticket',
  endpoint: 'InvoiceItem',
  fields: {
    invoiceId: { label: 'Invoice', type: 'external', required: true },
    description: { label: 'Description', type: 'textarea', required: true },
    unit: { label: 'Units', type: 'bigtext', required: true },
    rate: { label: 'Rate', type: 'currency', required: true },
    amount: { label: 'Amount', type: 'currency', disabled: true },
    currency: { label: 'Currency', type: 'codetype', disabled: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    invoiceId: {
      entity: 'Invoice',
    },
    currency: {
      typeCode: 'Currency',
      sync: (x, _, parent?: { value: Invoice }) => {
        if (!parent?.value) return
        const invoice = parent.value as Invoice
        x.value.currency = invoice.currency
      },
    },
    unit: {
      sync: (x) => (x.value.amount = round(x.value.unit * x.value.rate)),
    },
    rate: {
      currencyKey: 'currency',
      sync: (x) => (x.value.amount = round(x.value.unit * x.value.rate)),
    },
    amount: {
      currencyKey: 'currency',
    },
    ...TenantEntityInfo.options,
  },
  default: {
    description: '',
    invoiceId: '',
    unit: 0,
    rate: 0,
    amount: 0,
    currency: '',
  },
  columnsShown: new Set(['description', 'unit', 'rate', 'amount']),
  formLayout: [['invoiceId', 'unit'], ['rate', 'amount'], ['description']],
}

import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface ProjectDocument extends TenantEntity {
  projectId: string
  category: string
  fileName: string
  fileType: string
  documentId: string
  description: string
}

export const projectDocumentInfo: Info<ProjectDocument> = {
  typeName: 'Project Document',
  typeShortName: 'Document',
  nameKey: 'fileName',
  sortKey: 'fileName',
  backend: 'Project',
  endpoint: 'ProjectDocument',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    category: { label: 'Category', type: 'select', required: true },
    fileName: { label: 'File Name', type: 'bigtext', required: true },
    fileType: { label: 'File Type', type: 'select', required: true },
    documentId: { label: 'Document', type: 'file', required: true },
    description: { label: 'Description', type: 'textarea', required: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    projectId: { entity: 'Project' },
    category: { options: ['Contract', 'Invoice', 'Ticket', 'Other'] },
    fileType: { options: ['PDF', 'Word', 'Excel', 'Image', 'Other'] },
    documentId: {
      fileKey: 'fileBlob',
      sync: (x) => {
        const file = (x.value as any)['fileBlob'] as File
        if (!file) return
        x.value.fileName = file.name
        x.value.fileType = file.type
      },
    },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    category: '',
    fileName: '',
    fileType: '',
    documentId: '',
    description: '',
  },
  columnsShown: new Set(['fileName', 'fileType', 'documentId', 'category']),
  formLayout: [['projectId', 'category'], ['fileName', 'fileType'], ['documentId'], ['description']],
}

import VisFutureLogo from './VisFutureLogo.vue'

export default {
  title: 'VisFutureLogo',
  component: VisFutureLogo,
  tags: ['autodocs'],
}

export const Default = () => ({
  components: { VisFutureLogo: VisFutureLogo },
  template: `<VisFutureLogo start="#6B7AFE" end="#083CC6" />`,
})

export const White = () => ({
  components: { VisFutureLogo: VisFutureLogo },
  template: `<div class="bg-primary">
    <VisFutureLogo start="#FFF"/>
  </div>`,
})

export const Blue = () => ({
  components: { VisFutureLogo: VisFutureLogo },
  template: `<VisFutureLogo start="#0E41C9"/>`,
})

export const Height = () => ({
  components: { VisFutureLogo: VisFutureLogo },
  template: `<VisFutureLogo start="#6B7AFE" end="#083CC6" :height="48"/>`,
})

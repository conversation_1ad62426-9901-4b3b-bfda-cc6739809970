import { codes } from '../CurrencyCodes'
import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface TicketBillingPayment extends TenantEntity {
  ticketBillingId: string
  description: string
  sequence?: string
  amount: number
  estimatedDate?: Date
  currency?: string
}

export const ticketBillingPaymentInfo: Info<TicketBillingPayment> = {
  typeName: 'Ticket Billing Payment',
  nameKey: 'description',
  sortKey: 'estimatedDate',
  backend: 'Ticket',
  endpoint: 'BillingPayment',
  fields: {
    ticketBillingId: { label: 'Ticket Billing', type: 'external', required: true },
    description: { label: 'Description', type: 'bigtext', required: true },
    sequence: { label: 'Sequence', type: 'smalltext' },
    amount: { label: 'Amount', type: 'currency', required: true },
    estimatedDate: { label: 'Estimated Date', type: 'date' },
    currency: { label: 'Currency', type: 'select' },
    ...TenantEntityInfo.fields,
  },
  options: {
    amount: { currencyKey: 'currency' },
    currency: { options: Object.keys(codes) },
    ticketBillingId: { entity: 'TicketBilling' },
    ...TenantEntityInfo.options,
  },
  default: {
    ticketBillingId: '',
    description: '',
    amount: 0,
  },
  columnsShown: new Set(['description', 'sequence', 'amount', 'estimatedDate']),
  formLayout: [
    ['ticketBillingId', 'description'],
    ['sequence', 'amount'],
    ['estimatedDate', 'currency'],
  ],
}

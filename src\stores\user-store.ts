import { useLocalStorage } from '@vueuse/core'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { baseUrls } from '../services/api/requests'
import { EntityResponse } from '../services/api/entityResponse'

export const useUserStore = defineStore('user', () => {
  const jwtToken = useLocalStorage('jwtToken', '')
  const refreshToken = useLocalStorage('refreshToken', '')
  const currentTenantId = useLocalStorage('currentTenantId', '')

  // Other state
  const userName = ref('<PERSON><PERSON><PERSON>')
  const email = ref('<EMAIL>')
  const memberSince = ref('8/12/2020')
  const pfp = ref('https://picsum.photos/id/22/200/300')
  const is2FAEnabled = ref(false)
  const tenantList = ref<{ id: string; name: string }[]>([])

  async function login(userNameInput: string, password: string): Promise<boolean> {
    const res = await fetch(baseUrls.BaseBiz + '/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: userNameInput, password }),
    })

    const json = (await res.json()) as EntityResponse
    if (!json.isSuccess) return false

    const data = json.entity
    jwtToken.value = data.jwtToken
    refreshToken.value = data.refreshToken
    userName.value = userNameInput
    return true
  }

  async function logout() {
    try {
      await fetch(baseUrls.BaseBiz + '/Logout?userId=' + getUserIdFromToken(), {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${jwtToken.value}`,
        },
      })
    } catch (e) {
      console.warn('Logout failed on server:', e)
    }

    ;(jwtToken as any)
      .remove()(refreshToken as any)
      .remove()(currentTenantId as any)
      .remove()

    tenantList.value = []
    userName.value = ''
  }

  async function setTenant(tenantId: string): Promise<boolean> {
    const res = await fetch(baseUrls.BaseBiz + '/SetTenant', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${jwtToken.value}`,
      },
      body: JSON.stringify({ id: tenantId }),
    })

    if (res.ok) {
      currentTenantId.value = tenantId
      return true
    }

    return false
  }

  function getUserIdFromToken(): string {
    try {
      const payload = JSON.parse(atob(jwtToken.value.split('.')[1]))
      return payload.UserId || payload.userId
    } catch (e) {
      console.error('Invalid JWT token format:', e)
      return ''
    }
  }

  return {
    // state
    jwtToken,
    refreshToken,
    currentTenantId,
    userName,
    email,
    memberSince,
    pfp,
    is2FAEnabled,
    tenantList,

    // actions
    login,
    logout,
    setTenant,
    getUserIdFromToken,
  }
})

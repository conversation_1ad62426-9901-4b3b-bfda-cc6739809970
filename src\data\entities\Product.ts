import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { TenantEntityInfo } from './TenantEntity'
import { ProductReleaseTracking } from './ProductReleaseTracking'
import { ProductLicenseTracking } from './ProductLicenseTracking'
import { ProductDeploymentTracking } from './ProductDeploymentTracking'

export interface Product extends CodeEntity {
  name: string
  productType?: string
  status?: string
  productManager?: string
  description?: string
  productReleaseTrackings?: ProductReleaseTracking[]
  productLicenseTrackings?: ProductLicenseTracking[]
  productDeploymentTrackings?: ProductDeploymentTracking[]
}

export const ProductTypes = ['Core', 'Add On', 'Integration', 'Template'] as const
export type ProductType = (typeof ProductTypes)[number]
export const Statuses = ['Active', 'Inactive', 'Beta', 'Deprecated', 'Released', 'End of Life'] as const
export type Status = (typeof Statuses)[number]

export const productInfo: Info<Product> = {
  typeName: 'Product',
  nameKey: 'name',
  sortKey: 'code',
  sortAsc: true,
  backend: 'Ticket',
  endpoint: 'Product',
  formSize: 'medium',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    productType: { label: 'Type', type: 'select' },
    status: { label: 'Status', type: 'select' },
    productManager: { label: 'Manager', type: 'external' },
    description: { label: 'Description', type: 'textarea' },
    productReleaseTrackings: { label: 'Release Trackings', type: 'objlist' },
    productLicenseTrackings: { label: 'License Trackings', type: 'objlist' },
    productDeploymentTrackings: { label: 'Deployment Trackings', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    productType: { options: ProductTypes.slice() },
    status: { options: Statuses.slice() },
    productManager: { entity: 'Employee' },
    productReleaseTrackings: { entity: 'ProductReleaseTracking' },
    productLicenseTrackings: { entity: 'ProductLicenseTracking' },
    productDeploymentTrackings: { entity: 'ProductDeploymentTracking' },
    ...TenantEntityInfo.options,
  },
  default: {
    code: 'Auto-generated',
    name: '',
    productType: '',
    status: '',
    productManager: '',
  },
  columnsShown: new Set(['code', 'name', 'productType', 'status', 'productManager', 'description']),
  formLayout: [['code', 'name', 'productType'], ['status', 'productManager'], ['description']],
  tabList: {
    ProductReleaseTracking: { tabName: 'Release Trackings', relation: { id: 'productId' } },
    ProductLicenseTracking: { tabName: 'License Trackings', relation: { id: 'productId' } },
    ProductDeploymentTracking: { tabName: 'Deployment Trackings', relation: { id: 'productId' } },
  },
}

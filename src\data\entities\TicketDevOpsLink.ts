import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface TicketDevOpsLink extends TenantEntity {
  ticketId: string
  devOpsTicketId: string
  releaseOn?: Date
  releaseNotes?: string
}

export const ticketDevOpsLinkInfo: Info<TicketDevOpsLink> = {
  typeName: 'Ticket DevOps Link',
  nameKey: 'devOpsTicketId',
  sortKey: 'releaseOn',
  backend: 'Ticket',
  endpoint: 'TicketDevOpsLink',
  fields: {
    ticketId: { label: 'Ticket', type: 'external', required: true },
    devOpsTicketId: { label: 'DevOps Ticket ID', type: 'smalltext', required: true },
    releaseOn: { label: 'Release Date', type: 'date' },
    releaseNotes: { label: 'Release Notes', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    ticketId: { entity: 'Ticket' },
    ...TenantEntityInfo.options,
  },
  default: {
    ticketId: '',
    devOpsTicketId: '',
  },
  columnsShown: new Set(['devOpsTicketId', 'releaseOn', 'releaseNotes']),
  formLayout: [['ticketId', 'devOpsTicketId'], ['releaseOn'], ['releaseNotes']],
}

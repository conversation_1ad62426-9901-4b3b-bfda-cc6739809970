import { Info } from '../FieldTypes'
import { BaseEntity, BaseEntityInfo } from './BaseEntity'

export interface GlobalAdmin extends BaseEntity {
  loginName: string
  name: string
  password: string
  description?: string
  email: string
  mobile?: string
  mfa: boolean
  isActive: boolean
  imageId?: string
}

export const globalAdminInfo: Info<GlobalAdmin> = {
  typeName: 'Global Admin',
  nameKey: 'name',
  sortKey: 'loginName',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'GlobalAdmin',
  fields: {
    loginName: { label: 'Login Name', type: 'smalltext', required: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    password: { label: 'Password', type: 'smalltext', required: true },
    description: { label: 'Description', type: 'textarea' },
    email: { label: 'Email', type: 'email', required: true },
    mobile: { label: 'Mobile', type: 'smalltext' },
    mfa: { label: 'MFA', type: 'bool', required: true },
    isActive: { label: 'Active', type: 'bool', required: true },
    imageId: { label: 'Image', type: 'file' },
    ...BaseEntityInfo.fields,
  },
  options: {},
  default: {
    loginName: '',
    name: '',
    password: '',
    email: '',
    mfa: false,
    isActive: true,
  },
  columnsShown: new Set(['loginName', 'name', 'email', 'mfa', 'isActive']),
  formLayout: [
    ['loginName', 'name'],
    ['email', 'mobile'],
    ['mfa', 'isActive'],
    ['imageId', 'password'],
    ['description'],
  ],
}

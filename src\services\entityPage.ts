import { ref, UnwrapRef, nextTick, shallowRef } from 'vue'
import { BaseEntity } from '../data/entities/BaseEntity'
import { ModalOptions, useToast } from 'vuestic-ui'
import { capitalize } from './utils'
import { Info } from '../data/FieldTypes'
import { schema, SchemaKey } from '../data/schema'
import { BuildApi } from './api/useApi'
import { useFileDialog, UseFileDialogReturn } from '@vueuse/core'
import { getEntityName } from './externUtils'
import { downloadFile } from './fileUtils'

const { init: notify } = useToast()

/**
 * Binds the context of the specified methods to the given object.
 * @param {EntityPage<BaseEntity>} obj - The object to bind the context to.
 */
export const bindContext = (obj: EntityPage<BaseEntity>) => {
  const arr: (keyof EntityPage<BaseEntity>)[] = [
    'tryCatchNotify',
    'areYouSure',
    'editEntity',
    'addEntity',
    'onEntitySaved',
    'toggleEntities',
    'deleteEntity',
    'deleteEntities',
    'beforeEditFormCloses',
    'importExcel',
    'exportExcel',
  ]
  arr.forEach((key) => (obj[key] = (obj[key]! as any).bind(obj)))
}

/**
 * Class representing an entity page with CRUD operations.
 * @template T - The type of the entity.
 */
export class EntityPage<T extends BaseEntity> {
  entityToEdit = ref<T | null>(null)
  showEditForm = ref(false)
  tableRef = shallowRef()
  editFormRef = shallowRef()
  entityApi: BuildApi<T>
  info: Info<T>
  type: SchemaKey
  confirm: (options: ModalOptions) => Promise<boolean>
  preEditHook: (entity: T) => Promise<void> = async () => {}
  preDeleteHook: (entity: T) => Promise<void> = async () => {}
  excelUpload: UseFileDialogReturn

  /**
   * Creates an instance of EntityPage.
   * @param {typeof ApiType} entityApi - The API for the entity.
   * @param {any} confirm - The confirmation function.
   * @param {SchemaKey} type - The type of the entity.
   */
  constructor(entityApi: BuildApi<T>, confirm: any, type: SchemaKey) {
    this.entityApi = entityApi
    this.confirm = confirm
    this.type = type
    this.info = schema[type]
    this.excelUpload = useFileDialog({
      accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      multiple: false,
      reset: true,
    })
    this.excelUpload.onChange((files) => {
      if (files && files.length > 0) {
        this.importExcel(files[0])
      }
    })
  }

  async loadEntityById(id: string) {
    try {
      const entity = await this.entityApi.get(id)
      if (entity) {
        await this.editEntity(entity)
      } else {
        notify({
          message: `Entity not found with id: ${id}`,
          color: 'danger',
        })
      }
    } catch (e) {
      notify({
        message: `Failed to load entity: ${(e as Error).message}`,
        color: 'danger',
      })
      console.error(e)
    }
  }

  async tryCatchNotify(fn: () => Promise<any>, ingVerb: string, edVerb: string, numObj?: number) {
    const typeName = this.info.typeShortName ?? this.info.typeName
    try {
      await fn.bind(this)()
    } catch (e) {
      notify({
        message: 'Error ' + ingVerb + ' ' + typeName + (numObj ? ' objects' : '') + ': ' + (e as Error).message,
        color: 'danger',
      })
      console.error(e)
      return
    }
    notify({
      message: (numObj ? numObj + ' ' + typeName + ' objects' : typeName) + ' ' + edVerb,
      color: 'success',
    })
  }

  async areYouSure(verb: string, entityName?: string) {
    const typeName = this.info.typeShortName ?? this.info.typeName
    return await this.confirm({
      title: capitalize(verb) + ' ' + typeName + ' ' + entityName,
      message: 'Are you sure you want to' + verb + ' ' + typeName + '?',
      okText: capitalize(verb),
      size: 'small',
      maxWidth: '380px',
    })
  }
  async areYouSureList(verb: string, numObj: number) {
    const typeName = this.info.typeShortName ?? this.info.typeName
    return await this.confirm({
      title: capitalize(verb) + ' ' + numObj + ' ' + typeName + ' objects',
      message: 'Are you sure you want to' + verb + ' ' + typeName + ' objects' + '?',
      okText: capitalize(verb),
      size: 'small',
      maxWidth: '380px',
    })
  }

  /**
   * Edits an entity.
   * @param {T} entity - The entity to edit.
   */
  async editEntity(entity: T) {
    this.entityToEdit.value = entity as UnwrapRef<T>
    this.showEditForm.value = true
    try {
      await this.preEditHook(entity)
    } catch (e) {
      notify({
        message: `Error loading ${this.info.typeName}: ${(e as Error).message}`,
        color: 'danger',
      })
      console.error(e)
    }
  }

  /**
   * Adds a new entity.
   */
  async addEntity() {
    this.entityToEdit.value = null
    await nextTick()
    this.showEditForm.value = true
  }

  /**
   * Deletes an entity.
   * @param {T} entity - The entity to delete.
   */
  async deleteEntity(entity: T) {
    const name = getEntityName(entity, this.type)
    if (!(await this.areYouSure('delete', name))) {
      return
    }
    await this.tryCatchNotify(
      async () => {
        await this.preDeleteHook(entity)
        await this.entityApi.remove(entity.id!)
      },
      'deleting',
      'deleted',
    )
  }

  /**
   * Toggles an entity based on toggle key info.
   */
  async toggleEntities() {
    if (!this.info.toggle) {
      return
    }
    const entities: T[] = this.tableRef.value.selectedRows
    await this.tryCatchNotify(
      async () => {
        entities.forEach((entity) => this.info.toggle!(entity))
        await this.entityApi.updateList(entities)
        this.tableRef.value.selectedRows = []
      },
      'modifying',
      'modified',
      entities.length,
    )
  }

  /**
   * Deletes multiple entities.
   */
  async deleteEntities() {
    const entities: T[] = this.tableRef.value.selectedRows
    if (entities.length === 1) {
      this.deleteEntity(entities[0])
      return
    }
    if (!(await this.areYouSureList('delete', entities.length))) {
      return
    }
    await this.tryCatchNotify(
      async () => {
        entities.forEach((entity) => this.preDeleteHook(entity))
        await this.entityApi.removeList(entities.map((entity) => entity.id!))
      },
      'deleting',
      'deleted',
      entities.length,
    )
  }

  /**
   * Handles the event when an entity is saved.
   * @param {T} entity - The saved entity.
   */
  async onEntitySaved(entity: T, inline?: boolean) {
    this.showEditForm.value = false
    if ('id' in entity) {
      if (!inline && !this.editFormRef.value.hasUnsavedChanges) {
        notify({
          message: 'No changes to save',
          color: 'secondary',
        })
        return
      }
      await this.tryCatchNotify(async () => await this.entityApi.update(entity), 'updating', 'updated')
    } else {
      await this.tryCatchNotify(async () => await this.entityApi.add(entity), 'creating', 'created')
    }
  }

  /**
   * Handles the event when an entity is deleted.
   * @param {string} id - The ID of the entity.
   */
  async onEntityDelete(id: string) {
    if (!(await this.areYouSure('delete', id))) {
      return
    }
    this.showEditForm.value = false
    await this.tryCatchNotify(async () => await this.entityApi.remove(id), 'deleting', 'deleted')
  }

  async importExcel(file: File) {
    await this.tryCatchNotify(
      async () => {
        await this.entityApi.importExcel(file)
      },
      'importing',
      'imported',
    )
  }

  async exportExcel() {
    const file = await this.entityApi.exportExcel(this.entityApi.getAllFilters())
    downloadFile(file)
  }

  /**
   * Handles the event before the edit form closes.
   * @param {() => unknown} hide - The function to hide the form.
   */
  async beforeEditFormCloses(hide: () => unknown) {
    if (this.editFormRef.value.hasUnsavedChanges) {
      const agreed = await this.confirm({
        maxWidth: '380px',
        message: 'Form has unsaved changes. Are you sure you want to close it?',
        size: 'small',
      })
      if (agreed) {
        hide()
      }
    } else {
      hide()
    }
  }
}

import { round } from '../../services/utils'
import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { Company } from './Company'
import { TenantEntityInfo } from './TenantEntity'
import { InvoiceItem } from './InvoiceItem'
import { UseApi } from '../../services/api/useApi'

export interface Invoice extends CodeEntity {
  type: Type
  invoiceDate: Date
  status: Status
  creditFlag: boolean
  creditInvoiceId?: string
  creditDate?: Date
  creditReason?: string
  clientId: string
  clientName?: string
  clientAddress1?: string
  clientAddress2?: string
  clientCity?: string
  clientProvince?: string
  clientPostalCode?: string
  terms: string
  poNumber?: string
  csr?: string
  currency: string
  subTotal: number
  discountRate: number
  discountAmount: number
  taxRate: number
  taxAmount: number
  totalAmount: number
  invoiceItems?: InvoiceItem[]
}

export const Types = ['Order', 'Template'] as const
export type Type = (typeof Types)[number]
export const Statuses = ['Open', 'Cancel'] as const
export type Status = (typeof Statuses)[number]

export const invoiceInfo: Info<Invoice> = {
  nameKey: 'code',
  sortKey: 'code',
  backend: 'Ticket',
  endpoint: 'Invoice',
  typeName: 'Invoice',
  formSize: 'large',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    type: { label: 'Type', type: 'select', required: true },
    invoiceDate: { label: 'Invoice Date', type: 'date', required: true },
    status: { label: 'Status', type: 'select', required: true },
    creditFlag: { label: 'Credit Flag', type: 'bool', required: true },
    creditInvoiceId: { label: 'Credit Invoice', type: 'smalltext' },
    creditDate: { label: 'Credit Date', type: 'date' },
    creditReason: { label: 'Credit Reason', type: 'bigtext' },
    clientId: { label: 'Client', type: 'external', required: true },
    clientName: { label: 'Client Name', type: 'bigtext' },
    clientAddress1: { label: 'Primary Address', type: 'place' },
    clientAddress2: { label: 'Secondary Address', type: 'place' },
    clientCity: { label: 'City', type: 'place' },
    clientProvince: { label: 'Province', type: 'place' },
    clientPostalCode: { label: 'Postal Code', type: 'place' },
    terms: { label: 'Terms', type: 'textarea', required: true },
    poNumber: { label: 'PO Number', type: 'smalltext' },
    csr: { label: 'CSR', type: 'smalltext' },
    currency: { label: 'Currency', type: 'codetype' },
    subTotal: { label: 'Sub Total', type: 'currency', disabled: true },
    discountRate: { label: 'Discount Rate', type: 'percent', required: true },
    discountAmount: { label: 'Discount Amount', type: 'currency', disabled: true },
    taxRate: { label: 'Tax Rate', type: 'percent', required: true },
    taxAmount: { label: 'Tax Amount', type: 'currency', disabled: true },
    totalAmount: { label: 'Total Amount', type: 'currency', disabled: true },
    invoiceItems: { label: 'Invoice Items', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    type: { options: Types.slice() },
    status: { options: Statuses.slice() },
    clientId: {
      entity: 'Company',
      sync: (x, extern?) => {
        if (!extern?.value) return
        const company = extern.value as Company
        x.value.clientAddress1 = company.address1
        x.value.clientAddress2 = company.address2
        x.value.clientCity = company.city
        x.value.clientProvince = company.province
        x.value.clientPostalCode = company.postalCode
        x.value.currency = company.currency ?? x.value.currency
      },
    },
    currency: {
      typeCode: 'Currency',
      sync: async (x) => {
        if (x.value.id) {
          try {
            const api = await new UseApi<InvoiceItem>('InvoiceItem').build()
            api.filters.value = {
              fieldValues: {
                invoiceId: new Set([x.value.id]),
              },
            }
            await api.fetch()
            const items = api.objs.value
            if (items.length) {
              for (const item of items) {
                item.currency = x.value.currency
                await api.update(item)
              }
            }
          } catch (error) {
            console.error('Failed to update invoice items currency:', error)
          }
        }
      },
    },
    subTotal: {
      currencyKey: 'currency',
    },
    discountRate: { sync: (x) => (x.value.discountAmount = round(x.value.subTotal * x.value.discountRate * 0.01)) },
    discountAmount: {
      currencyKey: 'currency',
      sync: (x) => {
        x.value.taxAmount = round((x.value.subTotal - x.value.discountAmount) * x.value.taxRate * 0.01)
        x.value.totalAmount = round(x.value.subTotal - x.value.discountAmount + x.value.taxAmount)
      },
    },
    taxRate: {
      sync: (x) => (x.value.taxAmount = round((x.value.subTotal - x.value.discountAmount) * x.value.taxRate * 0.01)),
    },
    taxAmount: {
      currencyKey: 'currency',
      sync: (x) => (x.value.totalAmount = round(x.value.subTotal - x.value.discountAmount + x.value.taxAmount)),
    },
    totalAmount: {
      currencyKey: 'currency',
    },
    invoiceItems: {
      entity: 'InvoiceItem',
    },
    ...TenantEntityInfo.options,
  },
  default: {
    type: 'Order',
    code: 'Auto-generated',
    invoiceDate: new Date(),
    status: 'Open',
    creditFlag: false,
    clientId: '',
    terms: '',
    subTotal: 0,
    discountRate: 0,
    discountAmount: 0,
    taxRate: 0,
    taxAmount: 0,
    totalAmount: 0,
    currency: 'CAD',
  },
  columnsShown: new Set(['code', 'invoiceDate', 'status', 'subTotal', 'discountAmount', 'taxAmount', 'totalAmount']),
  formLayout: [
    ['type', 'code', 'creditInvoiceId', 'creditFlag'],
    ['invoiceDate', 'status', 'creditDate', 'creditReason'],
    ['terms'],
    ['clientId', 'clientName', 'clientCity', 'clientProvince'],
    ['clientAddress1', 'clientAddress2', 'clientPostalCode', 'poNumber'],
    ['currency', 'subTotal'],
    ['discountRate', 'discountAmount', 'taxRate', 'taxAmount'],
    ['csr', 'totalAmount'],
  ],
  tabList: {
    InvoiceItem: {
      tabName: 'Invoice Items',
      relation: { id: 'invoiceId', currency: 'currency' },
    },
  },
}

import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { ProductDeploymentTracking } from './ProductDeploymentTracking'

export interface ProductReleaseTracking extends TenantEntity {
  productId: string
  versionId?: string
  releaseId: string
  buildId?: string
  dateSent?: Date
  dateInstalled?: Date
  description?: string
  productDeploymentTrackings?: ProductDeploymentTracking[]
}

export const productReleaseTrackingInfo: Info<ProductReleaseTracking> = {
  typeName: 'Product Release Tracking',
  nameKey: 'releaseId',
  sortKey: 'productId',
  sortAsc: true,
  backend: 'Ticket',
  endpoint: 'ProductReleaseTracking',
  fields: {
    productId: { label: 'Product', type: 'external', required: true },
    releaseId: { label: 'Release ID', type: 'smalltext', required: true },
    versionId: { label: 'Version ID', type: 'smalltext' },
    buildId: { label: 'Build ID', type: 'smalltext' },
    dateSent: { label: 'Date Send', type: 'date' },
    dateInstalled: { label: 'Date Installed', type: 'date' },
    description: { label: 'Description', type: 'textarea' },
    productDeploymentTrackings: { label: 'Deployments', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    productId: { entity: 'Product' },
    productDeploymentTrackings: { entity: 'ProductDeploymentTracking' },
    ...TenantEntityInfo.options,
  },
  default: {
    productId: '',
    versionId: '',
    releaseId: '',
    buildId: '',
  },
  columnsShown: new Set(['productId', 'releaseId', 'versionId', 'buildId', 'dateSent', 'dateInstalled', 'description']),
  formLayout: [['productId', 'releaseId', 'versionId'], ['buildId', 'dateSent', 'dateInstalled'], ['description']],
  tabList: {
    ProductDeploymentTracking: { tabName: 'Deployments', relation: { productId: 'productId', id: 'productReleaseId' } },
  },
}

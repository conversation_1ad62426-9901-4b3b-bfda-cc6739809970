import { Ref, ref, unref, UnwrapRef, watch } from 'vue'
import { watchIgnorable } from '@vueuse/core'
import { AllFilters, Api, Filters, Pagination, Sorting } from './api'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { schema, SchemaKey } from '../../data/schema'
import { B64File } from '../fileUtils'
import { BaseKey } from '../../data/FieldTypes'

export interface BuildApi<T extends BaseEntity> {
  get: (id: string) => Promise<T>
  isLoading: Ref<boolean>
  filters: Ref<Partial<Filters>>
  objs: Ref<UnwrapRef<T[]>>
  fetch: () => Promise<void>
  getAllFilters: () => AllFilters<T>
  add: (obj: T) => Promise<string>
  addList: (objs: T[]) => Promise<boolean>
  update: (obj: T) => Promise<string>
  updateList: (objs: T[]) => Promise<boolean>
  remove: (id: string) => Promise<string>
  removeList: (ids: string[]) => Promise<boolean>
  importExcel: (file: File) => Promise<boolean>
  exportExcel: (filters: AllFilters<T>) => Promise<B64File>
  pagination: Ref<Pagination>
  sorting: Ref<Sorting<T>>
  ignoreUpdates: (cb: () => void) => void
}

/**
 * Class for using API with Vue.js reactive properties.
 * @template T - The type of the entity.
 */
export class UseApi<T extends BaseEntity> {
  api: Api<T>
  defaultPage = () => ref<Pagination>({ page: 1, perPage: 10, total: 0 })
  defaultSort: () => Ref<Sorting<T>>
  defaultFilters = () => ref<Partial<Filters>>({ fieldValues: {} })
  data: Ref<T[]> = ref([])

  /**
   * Creates an instance of UseApi.
   * @param {SchemaKey} type - The type of the entity.
   */
  constructor(type: SchemaKey) {
    this.api = new Api<T>(type)
    this.defaultSort = () =>
      ref({
        sortBy: this.api.info.sortKey as BaseKey,
        sortingOrder: schema[type].sortAsc ? 'asc' : 'desc',
      })
  }

  /**
   * Builds the API with options for sorting, pagination, and filters.
   * @param {Object} [options] - The options for building the API.
   * @param {Ref<Sorting<T>>} [options.sorting] - The sorting options.
   * @param {Ref<Pagination>} [options.pagination] - The pagination options.
   * @param {Ref<Partial<Filters>>} [options.filters] - The filter options.
   * @param {boolean} [options.noFetch] - Whether to skip the initial fetch.
   * @returns {Promise<Object>} The API methods and reactive properties.
   */
  build = async (options?: {
    sorting?: Ref<Sorting<T>>
    pagination?: Ref<Pagination>
    filters?: Ref<Partial<Filters>>
    noFetch?: boolean
  }): Promise<BuildApi<T>> => {
    const isLoading = ref(false)
    const objs = ref<T[]>([])

    const {
      filters = this.defaultFilters(),
      sorting = this.defaultSort(),
      pagination = this.defaultPage(),
    } = options ?? {}

    const getAllFilters = () => ({
      ...unref(filters),
      ...unref(sorting),
      ...unref(pagination),
    })

    const fetch = async () => {
      isLoading.value = true
      const { data, pagination: newPagination } = await this.api.getList(getAllFilters())
      objs.value = data as any
      ignoreUpdates(() => {
        pagination.value = newPagination
      })
      isLoading.value = false
    }
    watch(
      filters,
      async () => {
        // Reset pagination to first page when filters changed
        pagination.value.page = 1
        await fetch()
      },
      { deep: true },
    )

    const { ignoreUpdates } = watchIgnorable([pagination, sorting], fetch, { deep: true })

    if (!options?.noFetch) {
      // don't do first fetch if noFetch is set
      await fetch()
    }

    const callLoadFetch = async <T>(fn: (arg: any) => Promise<T>, arg: any) => {
      isLoading.value = true
      const result = await fn(arg)
      await fetch()
      isLoading.value = false
      return result
    }

    const api = this.api
    return {
      isLoading,
      filters,
      objs,
      fetch,
      getAllFilters,
      add: async (obj: T) => callLoadFetch(api.add, obj),
      addList: async (objs: T[]) => callLoadFetch(api.addList, objs),
      update: async (obj: T) => callLoadFetch(api.update, obj),
      updateList: async (objs: T[]) => callLoadFetch(api.updateList, objs),
      remove: async (id: string) => callLoadFetch(api.remove, id),
      removeList: async (ids: string[]) => callLoadFetch(api.removeList, ids),
      importExcel: async (file: File) => callLoadFetch(api.importExcel, file),
      exportExcel: async (filters: AllFilters<T>) => api.exportExcel(filters),
      pagination,
      sorting,
      ignoreUpdates,
      get: async (id: string) => api.get(id),
    }
  }
}

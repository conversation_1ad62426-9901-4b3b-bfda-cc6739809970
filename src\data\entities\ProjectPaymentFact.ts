import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { ProjectContract } from './ProjectContract'
import { round } from '../../services/utils'
import { ProjectPaymentPlan } from './ProjectPaymentPlan'

export interface ProjectPaymentFact extends TenantEntity {
  projectId: string
  payPlanId?: string
  contractId?: string
  payerId: string
  payeeId: string
  category?: string
  payMethod?: string
  transInfo?: string
  currency: string
  description?: string
  payDate: Date
  percentage: number
  amount: number
  taxRate: number
  taxAmount: number
  totalAmount: number
}

export const Categories = ['Advance', 'Milestone', 'Progress', 'Final', 'Other'] as const
export type Category = (typeof Categories)[number]
export const PayMethods = ['Bank Transfer', 'Cheque', 'Online', 'Cash', 'Other'] as const
export type SPayMethod = (typeof PayMethods)[number]

export const projectPaymentFactInfo: Info<ProjectPaymentFact> = {
  typeName: 'Project Payment Fact',
  nameKey: 'projectId',
  sortKey: 'contractId',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'ProjectPaymentFact',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    payPlanId: { label: 'Payment Plan ID (due)', type: 'external' },
    contractId: { label: 'Contract', type: 'external' },
    payerId: { label: 'Payer', type: 'external', required: true },
    payeeId: { label: 'Payee', type: 'external', required: true },
    payDate: { label: 'Paid Date', type: 'date', required: true },
    currency: { label: 'Currency', type: 'codetype', required: true },
    amount: { label: 'Contract Amount', type: 'currency', required: true },
    percentage: { label: 'Paid Percentage', type: 'percent', required: true },
    taxRate: { label: 'Tax Rate', type: 'percent', required: true },
    taxAmount: { label: 'Tax Amount', type: 'currency', disabled: true },
    totalAmount: { label: 'Total Paid Amount', type: 'currency', disabled: true },
    category: { label: 'Category', type: 'select' },
    payMethod: { label: 'Payment Method', type: 'select' },
    transInfo: { label: 'Transaction Info', type: 'textarea' },
    description: { label: 'Description', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    projectId: { entity: 'Project' },
    payPlanId: {
      entity: 'ProjectPaymentPlan',
      joins: { projectId: 'projectId' },
      sync: (x, extern?) => {
        if (!extern?.value) return
        const projectPaymentPlan = extern.value as ProjectPaymentPlan
        x.value.contractId = projectPaymentPlan.contractId
        x.value.category = projectPaymentPlan.category
        x.value.payMethod = projectPaymentPlan.payMethod
        x.value.amount = projectPaymentPlan.amount
        x.value.payerId = projectPaymentPlan.payerId
        x.value.payeeId = projectPaymentPlan.payeeId
        x.value.currency = projectPaymentPlan.currency
        x.value.taxRate = projectPaymentPlan.taxRate
      },
    },
    contractId: {
      entity: 'ProjectContract',
      joins: { projectId: 'projectId' },
      sync: (x, extern?) => {
        if (!extern?.value) return
        const projectContract = extern.value as ProjectContract
        x.value.amount = projectContract.amount
        x.value.payerId = projectContract.relatedPartyA
        x.value.payeeId = projectContract.relatedPartyB
        x.value.currency = projectContract.currency
        x.value.taxRate = projectContract.taxRate
      },
    },
    payerId: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    payeeId: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    category: { options: Categories.slice() },
    payMethod: { options: PayMethods.slice() },
    currency: { typeCode: 'Currency' },
    percentage: {
      sync: (x) => {
        x.value.taxAmount = round(x.value.amount * x.value.percentage * 0.01 * x.value.taxRate * 0.01)
        x.value.totalAmount = round(x.value.amount * x.value.percentage * 0.01 + x.value.taxAmount)
      },
    },
    amount: {
      currencyKey: 'currency',
      sync: (x) => {
        x.value.taxAmount = round(x.value.amount * x.value.percentage * 0.01 * x.value.taxRate * 0.01)
        x.value.totalAmount = round(x.value.amount * x.value.percentage * 0.01 + x.value.taxAmount)
      },
    },
    taxRate: {
      sync: (x) => (x.value.taxAmount = round(x.value.amount * x.value.percentage * 0.01 * x.value.taxRate * 0.01)),
    },
    taxAmount: {
      currencyKey: 'currency',
      sync: (x) => (x.value.totalAmount = round(x.value.amount * x.value.percentage * 0.01 + x.value.taxAmount)),
    },
    totalAmount: { currencyKey: 'currency' },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    payPlanId: '',
    contractId: '',
    payerId: '',
    payeeId: '',
    category: '',
    payMethod: '',
    currency: 'CAD',
    payDate: new Date(),
    percentage: 100,
    amount: 0,
    taxRate: 0,
    taxAmount: 0,
    totalAmount: 0,
  },
  columnsShown: new Set([
    'projectId',
    'contractId',
    'payPlanId',
    'payerId',
    'payeeId',
    'payDate',
    'category',
    'payMethod',
    'percentage',
    'amount',
    'totalAmount',
    'transInfo',
    'description',
  ]),
  formLayout: [
    ['projectId', 'payPlanId', 'contractId'],
    ['payerId', 'payeeId'],
    ['category', 'payMethod'],
    ['currency', 'payDate'],
    ['amount', 'percentage'],
    ['taxRate', 'taxAmount'],
    ['totalAmount'],
    ['transInfo'],
    ['description'],
  ],
}

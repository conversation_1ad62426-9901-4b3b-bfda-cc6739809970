<script setup lang="ts">
import { BaseEntity } from '../../data/entities/BaseEntity'
import { BadgeOp, BaseKey, CurrencyOp, FieldInfo, Info } from '../../data/FieldTypes'
import { schema, SchemaKey } from '../../data/schema'
import { getExternSchema, externTypes, getEntityName } from '../../services/externUtils'
import { CHIP_SIZE } from '../../data/Constants'
import { formatTime } from '../../services/dateTimeUtils'
import { trunc } from '../../services/utils'
import { computed } from 'vue'

const props = defineProps<{
  objKey: BaseKey
  type: SchemaKey
  rowData: BaseEntity
  field: FieldInfo
  externList: Record<string, Record<string, BaseEntity>>
}>()

const cell = computed(() => props.rowData[props.objKey])
const info = schema[props.type] as Info<BaseEntity>
const locale = navigator.language

const getExternVal = () => {
  const extern = props.externList[props.objKey]?.[cell.value as string]
  if (extern) {
    return trunc(getEntityName(extern, getExternSchema(props.type, props.objKey)), CHIP_SIZE)
  } else {
    return 'Loading...'
  }
}

const getCurrency = () =>
  (cell.value as any as number).toLocaleString(locale, {
    style: 'currency',
    currency: (props.rowData[(info.options![props.objKey] as CurrencyOp<BaseEntity>).currencyKey] as string) ?? 'CAD',
  })

const getPercent = () =>
  ((cell.value as any as number) / 100).toLocaleString(locale, {
    style: 'percent',
  })

const getBadgeColor = () => (info.options![props.objKey] as BadgeOp).badge[cell.value as string]
</script>
<template>
  <div v-if="cell !== undefined && cell !== null" class="w-fit h-fit">
    <span v-if="['bigtext', 'textarea'].includes(field.type)" class="max-w-[230px] ellipsis">
      {{ cell }}
    </span>
    <span v-else-if="['smalltext', 'select'].includes(field.type)" class="max-w-[120px] ellipsis">
      {{ cell }}
    </span>
    <VaChip
      v-else-if="field.type === 'objlist'"
      outline
      readonly
      size="small"
      class="flex items-center gap-2 max-w-[120px] ellipsis"
    >
      {{ (cell as string).length + ' Objects' }}
    </VaChip>
    <span v-else-if="field.type === 'datetime'" class="max-w-[120px] ellipsis">
      {{
        Intl.DateTimeFormat(locale, {
          dateStyle: 'short',
          timeStyle: 'short',
        }).format(new Date(cell as string))
      }}
    </span>
    <span v-else-if="field.type === 'date'" class="max-w-[120px] ellipsis">
      {{ new Date(cell as string).toLocaleDateString(locale) }}
    </span>
    <span v-else-if="field.type === 'time'" class="max-w-[120px] ellipsis">
      {{ formatTime(cell as any) }}
    </span>
    <span v-else-if="field.type === 'number'" class="max-w-[120px] ellipsis">
      {{ cell }}
    </span>
    <span v-else-if="field.type === 'currency'" class="max-w-[230px] ellipsis">
      {{ getCurrency() }}
    </span>
    <span v-else-if="field.type === 'bool'">
      <VaIcon :name="cell ? 'check_circle' : 'radio_button_unchecked'" :size="'1.5rem'" />
    </span>
    <span
      v-else-if="field.type === 'email'"
      class="max-w-[230px] ellipsis underline text-blue-600 hover:text-blue-800 visited:text-purple-600"
    >
      <a :href="'mailto:' + cell">{{ cell }}</a>
    </span>
    <span v-else-if="field.type === 'percent'" class="max-w-[230px] ellipsis">
      {{ getPercent() }}
    </span>
    <span v-else-if="field.type === 'badge'">
      <VaBadge :color="getBadgeColor()" :text="(cell as string) + ''" />
    </span>
    <span
      v-else-if="field.type === 'tel'"
      class="max-w-[230px] ellipsis underline text-blue-600 hover:text-blue-800 visited:text-purple-600"
    >
      <a :href="'tel:' + cell">{{ cell }}</a>
    </span>
    <span
      v-else-if="field.type === 'place'"
      class="max-w-[230px] ellipsis underline text-blue-600 hover:text-blue-800 visited:text-purple-600"
    >
      <a :href="'https://www.google.com/maps/search/' + cell" target="_blank">
        {{ cell }}
      </a>
    </span>
    <span
      v-else-if="field.type === 'url'"
      class="max-w-[230px] ellipsis underline text-blue-600 hover:text-blue-800 visited:text-purple-600"
    >
      <a :href="(cell as string) + ''" target="_blank">{{ cell }}</a>
    </span>
    <VaPopover v-else-if="field.type === 'code'" class="max-w-[120px] ellipsis" :message="getExternVal()">
      <span>
        {{ cell }}
      </span>
    </VaPopover>
    <span v-else-if="externTypes.includes(field.type)" class="max-w-[230px] ellipsis">
      {{ getExternVal() }}
    </span>
    <span v-else class="max-w-[230px] ellipsis">
      <!-- default fallback -->
      {{ cell }}
    </span>
  </div>
</template>

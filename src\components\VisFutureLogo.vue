<template>
  <img src="/visfuture.png" alt="VisFuture" width="192" />
</template>

<script lang="ts" setup>
// import { computed } from 'vue'
// import { useColors } from 'vuestic-ui'

// const { getColor } = useColors()

// const props = withDefaults(
//   defineProps<{
//     // class?: string
//     height?: number
//     start?: string
//     end?: string
//   }>(),
//   {
//     height: 18,
//     start: 'primary',
//     end: undefined,
//   },
// )

// const colorsComputed = computed(() => {
//   return {
//     start: getColor(props.start),
//     end: getColor(props.end || props.start),
//   }
// })
// console.log(colorsComputed) // to make the git lint errors go away
</script>

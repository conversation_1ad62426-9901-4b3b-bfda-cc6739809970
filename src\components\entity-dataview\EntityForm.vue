<script setup lang="ts">
import { computed, ref } from 'vue'
import { useForm } from 'vuestic-ui'
import { Info, BaseKey } from '../../data/FieldTypes'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { schema, SchemaKey } from '../../data/schema'

import EntityFormInput from './EntityFormInput.vue'

const props = defineProps<{
  type: SchemaKey
  entity: BaseEntity | null
  defaults?: any
  saveButtonLabel: string
}>()

const emit = defineEmits<{
  (event: 'save', entity: BaseEntity): void
  (event: 'delete', id: string): void
  (event: 'close'): void
}>()

const info = schema[props.type] as Info<BaseEntity>
const newEntity = ref<BaseEntity>({ ...info.default, ...props.entity, ...props.defaults })
const formLayout = info.formLayout

const hasUnsavedChanges = computed(() => {
  return Object.keys(newEntity.value).some(
    (key) => newEntity.value[key as BaseKey] !== { ...info.default, ...props.entity, ...props.defaults }?.[key],
  )
})

defineExpose({ hasUnsavedChanges })

const form = useForm('add-entity-form')

const onSave = async () => {
  if (!form.validate()) {
    return
  }
  emit('save', newEntity.value)
}
</script>

<template>
  <VaForm
    v-slot="{ isValid }"
    ref="add-entity-form"
    class="flex-col justify-start items-start gap-4 inline-flex w-full"
  >
    <slot name="top" />
    <div v-for="row in formLayout" :key="row.toString()" class="flex gap-4 flex-col sm:flex-row w-full">
      <template v-for="key in row" :key="key">
        <Suspense>
          <template #default>
            <EntityFormInput v-model="newEntity" :obj-key="key" :type="type" :default="defaults?.[key]" />
          </template>
          <template #fallback>
            <VaSkeleton inline height="55px" />
          </template>
        </Suspense>
      </template>
    </div>
    <slot name="bottom" />
    <div class="flex-col justify-start items-start gap-4 inline-flex w-full sm:flex-row sm:justify-between">
      <div v-if="newEntity.id" class="justify-start inline-flex w-full flex-col sm:flex-row">
        <VaButton preset="primary" color="danger" @click="emit('delete', newEntity.id!)">Delete</VaButton>
      </div>
      <div class="justify-end inline-flex w-full flex-col sm:flex-row">
        <slot name="button-row" />
        <VaButton preset="secondary" color="secondary" @click="emit('close')">Cancel</VaButton>
        <VaButton :disabled="!isValid" @click="onSave">{{ saveButtonLabel }}</VaButton>
      </div>
    </div>
  </VaForm>
</template>

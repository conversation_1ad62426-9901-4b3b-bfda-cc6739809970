import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { I18nTranslation } from './I18nTranslation'

export interface I18nKey extends TenantEntity {
  keyCode: string
  keyType: string
  defaultValue?: string
  i18nTranslations?: I18nTranslation[]
}

export const i18nKeyInfo: Info<I18nKey> = {
  typeName: 'I18n Key',
  nameKey: 'keyCode',
  sortKey: 'keyCode',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'I18nKey',
  fields: {
    keyCode: { label: 'Key Code', type: 'smalltext', required: true },
    keyType: { label: 'Key Type', type: 'codetype', required: true },
    defaultValue: { label: 'Default Value', type: 'textarea' },
    i18nTranslations: { label: 'Translations', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    keyType: { typeCode: 'KeyType' },
    i18nTranslations: { entity: 'I18nTranslation' },
    ...TenantEntityInfo.options,
  },
  default: {
    keyCode: '',
    keyType: '',
    defaultValue: '',
  },
  columnsShown: new Set(['keyCode', 'keyType', 'defaultValue']),
  formLayout: [['keyCode', 'keyType'], ['defaultValue']],
  tabList: {
    I18nTranslation: { tabName: 'Translations', relation: { id: 'keyId' } },
  },
}

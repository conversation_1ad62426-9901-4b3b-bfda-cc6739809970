import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { Employee } from './Employee'
import { Organization } from './Organization'

export interface OrganizationEmployee extends TenantEntity {
  organizationId: string
  employeeId: string
  employee?: Employee
  employeeName?: string
  organization?: Organization[]
}

export const organizationEmployeeInfo: Info<OrganizationEmployee> = {
  typeName: 'Organization Employee',
  nameKey: 'employee',
  sortKey: 'employeeId',
  backend: 'BaseBiz',
  endpoint: 'OrganizationEmployee',
  fields: {
    organizationId: { label: 'Organization', type: 'external', required: true },
    employeeId: { label: 'Employee', type: 'filtered', required: true },
    employeeName: { label: 'Employee', type: 'smalltext' },
    employee: { label: 'Employee', type: 'external' },
    organization: { label: 'Organization', type: 'external' },
    ...TenantEntityInfo.fields,
  },
  options: {
    organizationId: { entity: 'OrganizationHierarchy' },
    employeeId: {
      entity: 'Employee',
      filteredEndpoint: 'EmployeeListByOrganizationList', //  Use filtered backend endpoint
      extraInfo: {
        organizationId: 'organizationId', //  map from local field to request
      },
    },
    ...TenantEntityInfo.options,
  },
  default: {
    organizationId: '',
    employeeId: '',
  },
  columnsShown: new Set(['organizationId', 'employeeName']),
  formLayout: [['organizationId', 'employeeId']],
}

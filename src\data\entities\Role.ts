import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { AssignableRole } from './AssignableRole'
import { RoleAccess } from './RoleAccess'
import { RoleAssignment } from './RoleAssignment'
import { TenantEntityInfo } from './TenantEntity'

export interface Role extends CodeEntity {
  name: string
  usage: RoleUsage
  description?: string
  isActive: boolean
  assignableRoles?: AssignableRole[]
  roleAccesses?: RoleAccess[]
  roleAssignments?: RoleAssignment[]
}

export const RoleUsages = ['Organization', 'Project', 'Both'] as const
export type RoleUsage = (typeof RoleUsages)[number]

export const roleInfo: Info<Role> = {
  typeName: 'Role',
  nameKey: 'name',
  sortKey: 'code',
  sortAsc: true,
  backend: 'BaseBiz',
  endpoint: 'Role',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    usage: { label: 'Usage', type: 'codetype', required: true },
    isActive: { label: 'Active', type: 'bool', required: true },
    description: { label: 'Description', type: 'textarea' },
    assignableRoles: { label: 'Assignable Roles', type: 'objlist' },
    roleAccesses: { label: 'Role Accesses', type: 'objlist' },
    roleAssignments: { label: 'Role Assignments', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    assignableRoles: { entity: 'AssignableRole' },
    roleAccesses: { entity: 'RoleAccess' },
    roleAssignments: { entity: 'RoleAssignment' },
    usage: { typeCode: 'RoleUsage' },
    ...TenantEntityInfo.options,
  },
  default: {
    code: 'Auto-generated',
    name: '',
    usage: 'Organization',
    isActive: true,
  },
  columnsShown: new Set(['code', 'name', 'usage', 'isActive', 'description']),
  formLayout: [['name', 'code'], ['usage', 'isActive'], ['description']],
  defaultFilter: 'usage',
}

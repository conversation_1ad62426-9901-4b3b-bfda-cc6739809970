import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface TicketLink extends TenantEntity {
  ticketId: string
  linkedId: string
  reason?: string
}

export const ticketLinkInfo: Info<TicketLink> = {
  typeName: 'Ticket Link',
  nameKey: 'reason',
  sortKey: 'createAt',
  backend: 'Ticket',
  endpoint: 'TicketLink',
  fields: {
    ticketId: { label: 'From Ticket', type: 'external', required: true },
    linkedId: { label: 'To Ticket', type: 'external', required: true },
    reason: { label: 'Reason', type: 'bigtext' },
    ...TenantEntityInfo.fields,
  },
  options: {
    ticketId: { entity: 'Ticket' },
    linkedId: { entity: 'Ticket' },
    ...TenantEntityInfo.options,
  },
  default: {
    ticketId: '',
    linkedId: '',
  },
  columnsShown: new Set(['ticketId', 'linkedId', 'reason']),
  formLayout: [['ticketId', 'linkedId'], ['reason']],
}

<script lang="ts" setup>
import { BaseEntity } from '../../data/entities/BaseEntity'
import { schema, SchemaKey } from '../../data/schema'
import { BuildApi } from '../../services/api/useApi'
import { EntityPage } from '../../services/entityPage'
import { getTypeName } from '../../services/utils'
import { useVueToPrint } from 'vue-to-print'

const props = defineProps<{
  type: SchemaKey
  api: BuildApi<BaseEntity>
  page: EntityPage<BaseEntity>
}>()

const info = schema[props.type]
const { handlePrint } = useVueToPrint({
  content: props.page.tableRef,
  documentTitle: 'PDF Export',
})
</script>

<template>
  <div class="flex flex-col sm:flex-row gap-2 mb-2 justify-between">
    <div class="flex flex-col sm:flex-row gap-2 justify-start">
      <div v-if="page.tableRef.value?.selectedRows.length > 0" class="flex flex-col md:flex-row gap-2 justify-start">
        <VaButton v-if="info.toggleKey" icon="toggle_on" color="info" @click="page.toggleEntities">
          Toggle {{ info.fields[info.toggleKey].label }}
        </VaButton>
        <VaButton icon="delete" color="danger" @click="page.deleteEntities"> Delete </VaButton>
      </div>
      <div class="flex flex-col md:flex-row gap-2 justify-start">
        <VaButton icon="download" color="secondary" @click="page.exportExcel"> Excel Export </VaButton>
        <VaButton icon="receipt_long" color="secondary" @click="handlePrint"> PDF Export </VaButton>
      </div>
      <slot name="left" />
    </div>

    <div class="flex flex-col md:flex-row gap-2 justify-end">
      <slot name="right" />
      <VaButton icon="upload_file" color="secondary" @click="page.excelUpload.open"> Excel Import </VaButton>
      <VaButton icon="add" gradient @click="page.addEntity">
        {{ getTypeName(type) }}
      </VaButton>
    </div>
  </div>
</template>

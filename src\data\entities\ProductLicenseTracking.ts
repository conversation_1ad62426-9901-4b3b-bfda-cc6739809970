import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { TenantEntityInfo } from './TenantEntity'

export interface ProductLicenseTracking extends CodeEntity {
  productId: string
  clientId: string
  materialManager?: number
  materialManagerViewOnly?: number
  pollManager?: number
  scaleManager?: number
  touchScreen?: number
  touchScreenPay?: number
  craneStationManager?: number
  licenseType?: string
  maxUsers?: number
  maxProjects?: number
  status?: string
  modulesEnabled?: string
  effectiveDate?: Date
  expireDate?: Date
  description?: string
}

export const LicenseTypes = [
  'Perpetual',
  'Subscription',
  'Trial',
  'Per User',
  'Per Device',
  'Site',
  'Enterprise',
] as const
export type LicenseType = (typeof LicenseTypes)[number]
export const Statuses = ['Planned', 'In Progress', 'Completed', 'Failed'] as const
export type Status = (typeof Statuses)[number]

export const productLicenseTrackingInfo: Info<ProductLicenseTracking> = {
  typeName: 'Product License Tracking',
  nameKey: 'productId',
  sortKey: 'code',
  sortAsc: true,
  backend: 'Ticket',
  endpoint: 'ProductLicenseTracking',
  fields: {
    productId: { label: 'Product', type: 'external', required: true },
    code: { label: 'License Code', type: 'smalltext', disabled: true },
    clientId: { label: 'Client ID', type: 'external', required: true },
    licenseType: { label: 'License Type', type: 'select' },
    status: { label: 'status', type: 'select' },
    materialManager: { label: 'Material Manager', type: 'smalltext' },
    materialManagerViewOnly: { label: 'Material Manager View Only', type: 'smalltext' },
    pollManager: { label: 'Poll Manager', type: 'smalltext' },
    scaleManager: { label: 'Scale Manager', type: 'smalltext' },
    touchScreen: { label: 'Touch Screen', type: 'smalltext' },
    touchScreenPay: { label: 'Touch Screen Pay', type: 'smalltext' },
    craneStationManager: { label: 'Crane Station Manager', type: 'smalltext' },
    maxUsers: { label: 'Max Users', type: 'smalltext' },
    maxProjects: { label: 'Max Projects', type: 'smalltext' },
    modulesEnabled: { label: 'Modules Enabled', type: 'smalltext' },
    effectiveDate: { label: 'Effective Date', type: 'date' },
    expireDate: { label: 'Exire Date', type: 'date' },
    description: { label: 'Description', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    productId: { entity: 'Product' },
    clientId: { entity: 'Company' },
    licenseType: { options: LicenseTypes.slice() },
    status: { options: Statuses.slice() },
    ...TenantEntityInfo.options,
  },
  default: {
    productId: '',
    code: 'Auto-generated',
    clientId: '',
    materialManager: 0,
    materialManagerViewOnly: 0,
    pollManager: 0,
    scaleManager: 0,
    touchScreen: 0,
    touchScreenPay: 0,
    craneStationManager: 0,
    licenseType: '',
    maxUsers: 0,
    maxProjects: 0,
    status: '',
    modulesEnabled: '',
    effectiveDate: new Date(),
  },
  columnsShown: new Set([
    'productId',
    'code',
    'clientId',
    'materialManager',
    'pollManager',
    'scaleManager',
    'licenseType',
    'status',
    'effectiveDate',
    'expireDate',
  ]),
  formLayout: [
    ['productId', 'code', 'clientId'],
    ['licenseType', 'status', 'modulesEnabled'],
    ['materialManager', 'materialManagerViewOnly', 'pollManager'],
    ['scaleManager', 'touchScreen', 'touchScreenPay'],
    ['craneStationManager', 'maxUsers', 'maxProjects'],
    ['effectiveDate', 'expireDate'],
    ['description'],
  ],
}

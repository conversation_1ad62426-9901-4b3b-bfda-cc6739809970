import { BaseEntity } from '../../data/entities/BaseEntity'
import { Info } from '../../data/FieldTypes'
import { schema, SchemaKey } from '../../data/schema'
import { toUtc } from '../dateTimeUtils'
import { B64File, fileToB64 } from '../fileUtils'
import { EntityResponse } from './entityResponse'
import { authedRequest } from './requests'

export type Pagination = {
  page: number
  perPage: number
  total: number
}
export type Sorting<T extends BaseEntity> = {
  sortBy: keyof T & string
  sortingOrder: 'asc' | 'desc' | null
}
export interface Filters {
  filterString: string
  isActive: boolean
  fieldValues: Record<string, Set<string | null>>
}
interface GuidResponse extends EntityResponse {
  entity: string
}
export type AllFilters<T extends BaseEntity> = Partial<Filters & Pagination & Sorting<T>>

/**
 * API class for handling CRUD operations on entities.
 * @template T - The type of the entity.
 */
export class Api<T extends BaseEntity> {
  info: Info<T>

  /**
   * Creates an instance of Api.
   * @param {SchemaKey} type - The type of the entity.
   */
  constructor(type: SchemaKey) {
    this.info = schema[type]
  }

  /**
   * Prepares an object for saving by removing undefined, null, or empty string properties.
   * @param {T} obj - The object to prepare.
   * @returns {T} The prepared object.
   */
  preSave = (obj: T): T => {
    for (const key in obj) {
      if (obj[key] === undefined || obj[key] === null || obj[key] === '') {
        delete obj[key]
      }
    }
    return obj
  }

  /**
   * Adds a new entity.
   * @param {T} obj - The entity to add.
   * @returns {Promise<string>} The ID of the added entity.
   */
  add = async (obj: T): Promise<string> =>
    ((await authedRequest(this.info.backend, '/Add' + this.info.endpoint, 'PUT', this.preSave(obj))) as GuidResponse)
      .entity

  /**
   * Adds a list of new entities.
   * @param {T[]} obj - The entities to add.
   * @returns {Promise<boolean>} Whether the entities were added successfully.
   */
  addList = async (obj: T[]): Promise<boolean> =>
    (
      await authedRequest(this.info.backend, '/Add' + this.info.endpoint + 'List', 'POST', {
        dtoList: obj.map(this.preSave),
      })
    ).entity

  /**
   * Updates an existing entity.
   * @param {T} obj - The entity to update.
   * @returns {Promise<string>} The ID of the updated entity.
   */
  update = async (obj: T): Promise<string> =>
    ((await authedRequest(this.info.backend, '/Update' + this.info.endpoint, 'PUT', this.preSave(obj))) as GuidResponse)
      .entity

  /**
   * Updates a list of existing entities.
   * @param {T[]} obj - The entities to update.
   * @returns {Promise<boolean>} Whether the entities were updated successfully.
   */
  updateList = async (obj: T[]): Promise<boolean> =>
    (
      await authedRequest(this.info.backend, '/Update' + this.info.endpoint + 'List', 'POST', {
        dtoList: obj.map(this.preSave),
      })
    ).entity

  /**
   * Removes an entity by ID.
   * @param {string} id - The ID of the entity to remove.
   * @returns {Promise<string>} The ID of the removed entity.
   */
  remove = async (id: string): Promise<string> =>
    (
      (await authedRequest(
        this.info.backend,
        '/Delete' + this.info.endpoint + '?' + this.info.endpoint + 'Id=' + id,
        'DELETE',
      )) as GuidResponse
    ).entity

  /**
   * Removes a list of entities by ID.
   * @param {string[]} ids - The IDs of the entities to remove.
   * @returns {Promise<boolean>} Whether the entities were removed successfully.
   */
  removeList = async (ids: string[]): Promise<boolean> =>
    (
      await authedRequest(this.info.backend, '/Delete' + this.info.endpoint + 'List', 'POST', {
        idList: ids,
      })
    ).entity

  /**
   * Imports entities from an Excel file.
   * @param {File} file - The Excel file to import.
   * @returns {Promise<boolean>} Whether the import was successful.
   */
  importExcel = async (file: File): Promise<boolean> => {
    // console.log(file)
    // console.log(await fileToB64(file))
    const response = await authedRequest(
      this.info.backend,
      '/Import' + this.info.endpoint + 'List',
      'POST',
      await fileToB64(file),
    )
    return response.isSuccess
  }

  /**
   * Exports entities to an Excel file.
   * @param {AllFilters<T>} filters - The filters to apply when exporting.
   * @returns {Promise<B64File>} The exported Excel file.
   */
  exportExcel = async (filters: AllFilters<T>): Promise<B64File> => {
    return (await this.queryList(filters, '/export' + this.info.endpoint + 'List')).entity
  }

  /**
   * Queries the list of entities based on filters, pagination, and sorting.
   * @param {AllFilters<T>} filters - The filters to apply when querying.
   * @param {string} fullEndpoint - The full endpoint to query.
   * @returns {Promise<EntityResponse>} The response from the server.
   */
  private queryList = async (filters: AllFilters<T>, fullEndpoint: string): Promise<EntityResponse> => {
    const { sortBy, sortingOrder, page = 1, perPage = 10, filterString, isActive, fieldValues, ...other } = filters
    delete other.total
    const fieldValuesArray = Object.fromEntries(
      // convert sets to arrays
      Object.entries(fieldValues ?? {}).map(([key, value]) => [key, Array.from(value)]),
    ) as Record<string, string[]>
    return await authedRequest(this.info.backend, fullEndpoint, 'POST', {
      pageSize: perPage,
      pageIndex: page,
      sorts: [
        {
          sortField: sortBy!,
          sortDirection: sortingOrder === 'asc' ? 0 : 1,
        },
      ],
      filterString,
      isActive,
      fieldValues: fieldValuesArray,
      ...other,
    })
  }
  /**
   * Gets a single entity by ID.
   * @param {string} id - The ID of the entity.
   * @returns {Promise<T>} The fetched entity.
   */
  get = async (id: string): Promise<T> => {
    const result = await authedRequest(
      this.info.backend,
      `/${this.info.endpoint}Details?${this.info.endpoint}Id=${id}`,
      'GET',
    )
    return result.entity
  }
  /**
   * Retrieves entities based on filters, pagination, and sorting.
   * @param {AllFilters<T>} filters - The filters, pagination, and sorting options.
   * @returns {Promise<{ data: T[], pagination: Pagination }>} The retrieved entities and pagination info.
   */
  getList = async (filters: AllFilters<T>): Promise<{ data: T[]; pagination: Pagination }> => {
    const { page = 1, perPage = 10 } = filters
    const response = await this.queryList(filters, '/' + this.info.endpoint + 'List')
    const dateFixed = response.entity.map((item: T) => {
      // convert dates so js knows they are in UTC
      const newItem = { ...item } as T
      Object.keys(item).forEach((key) => {
        const _key = key as keyof T & string
        const field = this.info.fields[_key]
        if (field && (field.type === 'datetime' || field.type === 'date') && item[_key]) {
          ;(newItem[_key] as Date) = toUtc(item[_key])
        }
      })
      return newItem
    })

    return {
      // make js read utc dates correctly
      data: dateFixed,
      pagination: {
        page,
        perPage,
        total: response.total!,
      },
    }
  }
}

import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface TicketDiscussion extends TenantEntity {
  ticketId: string
  type: DiscussionType
  replyDate: Date
  author: string
  sender: string
  receiver: string
  cc?: string
  template?: string
  title: string
  comment: string
  isPublic: boolean
}

export const DiscussionTypes = ['Comment', 'Email', 'Phone', 'Meeting'] as const
export type DiscussionType = (typeof DiscussionTypes)[number]

export const ticketDiscussionInfo: Info<TicketDiscussion> = {
  typeName: 'Ticket Discussion',
  nameKey: 'title',
  sortKey: 'replyDate',
  backend: 'Ticket',
  endpoint: 'TicketDiscussion',
  fields: {
    title: { label: 'Title', type: 'bigtext', required: true },
    ticketId: { label: 'Ticket', type: 'external', required: true },
    type: { label: 'Type', type: 'smalltext', required: true },
    replyDate: { label: 'Reply Date', type: 'date', required: true },
    author: { label: 'Author', type: 'smalltext', required: true },
    sender: { label: 'Sender', type: 'smalltext', required: true },
    receiver: { label: 'Receiver', type: 'smalltext', required: true },
    cc: { label: 'CC', type: 'smalltext' },
    template: { label: 'Template', type: 'smalltext' },
    comment: { label: 'Comment', type: 'textarea', required: true },
    isPublic: { label: 'Public', type: 'bool', required: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    ticketId: { entity: 'Ticket' },
    ...TenantEntityInfo.options,
  },
  default: {
    ticketId: '',
    type: 'Comment',
    replyDate: new Date(),
    author: '',
    sender: '',
    receiver: '',
    title: '',
    comment: '',
    isPublic: true,
  },
  columnsShown: new Set(['type', 'replyDate', 'author', 'title', 'isPublic']),
  formLayout: [
    ['title', 'author'],
    ['sender', 'receiver'],
    ['ticketId', 'replyDate'],
    ['type', 'isPublic'],
    ['cc', 'template'],
    ['comment'],
  ],
}

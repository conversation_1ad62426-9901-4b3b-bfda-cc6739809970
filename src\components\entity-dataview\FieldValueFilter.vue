<script setup lang="ts">
import { Ref } from 'vue'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { schema, SchemaKey } from '../../data/schema'
import { trunc } from '../../services/utils'
import { getEntityName, isExtern } from '../../services/externUtils'

const props = defineProps<{
  objType: SchemaKey
  fieldName: string
  valueItems: (string | null)[]
  valueEntities: BaseEntity[]
  clearable?: boolean
}>()
const fieldValue = defineModel({ required: true }) as Ref<string | null>
const field = schema[props.objType].fields[props.fieldName]
const valueBy = field.type === 'codetype' ? 'value' : 'id'
</script>
<template>
  <VaSelect
    v-if="isExtern(objType, fieldName)"
    v-model="fieldValue"
    :value-by="valueBy"
    :options="valueEntities"
    :text-by="(x: any) => getEntityName(x, objType)"
    :clearable="clearable"
    :placeholder="'Value of ' + field.label"
    searchable
  />
  <VaSelect
    v-else
    v-model="fieldValue"
    text-by="text"
    value-by="value"
    :options="
      valueItems.map((item) => {
        return { text: trunc(item, 50), value: item }
      })
    "
    :clearable="clearable"
    :placeholder="'Value of ' + field.label"
    searchable
  />
</template>

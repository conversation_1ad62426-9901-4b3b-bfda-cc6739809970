# Project Documentation

This document provides an overview of key components and services in the project, focusing on the `src/components/entity-dataview/` and `src/services/api/` directories.

This was written with Aug<PERSON>, but I have checked over it and made sure it was accurate.

-- William

## Table of Contents

- [Entity Dataview Components](#entity-dataview-components)
  - [Overview](#overview)
  - [Component Descriptions](#component-descriptions)
- [Entity Page Service](#entity-page-service)
  - [Overview](#overview-1)
  - [EntityPage Class](#entitypage-class)
  - [Integration with Components](#integration-with-components)
  - [Helper Functions](#helper-functions)
- [API Services](#api-services)
  - [Overview](#api-overview)
  - [Service Descriptions](#service-descriptions)
- [Integration Examples](#integration-examples)

## Entity Dataview Components

### Overview

The `entity-dataview` directory contains a collection of Vue components that provide a comprehensive data management interface for entities in the application. These components work together to create a complete CRUD (Create, Read, Update, Delete) interface with filtering, sorting, and pagination capabilities.

### Component Descriptions

#### EntityComponent.vue

The main container component that orchestrates the entity data management interface. It integrates filtering, table display, and form editing capabilities.

**Key Features:**

- Integrates with the API service layer
- Manages entity state through the EntityPage service
- Provides slots for customization
- Handles modal dialogs for entity editing

#### EntityTable.vue

Displays entity data in a tabular format with sorting, selection, and action capabilities.

**Key Features:**

- Sortable columns
- Row selection
- Inline editing
- Action buttons for edit/delete operations
- Integration with pagination
- Column visibility toggle

#### EntityForm.vue

Provides a form interface for creating and editing entities.

**Key Features:**

- Dynamic form generation based on entity schema
- Validation
- Save/cancel operations
- Delete capability for existing entities
- Customizable layout through slots

#### EntityFormInput.vue

Renders appropriate input controls based on field types defined in the entity schema.

**Key Features:**

- Supports multiple input types (text, select, date, etc.)
- Handles validation
- Supports inline editing mode
- Integrates with external data sources

#### EntityFilters.vue

Provides filtering capabilities for entity lists.

**Key Features:**

- Field selection for filtering
- Value selection based on field type
- Multiple filter application
- Filter visualization with chips

#### EntityModify.vue

Provides action buttons for entity operations like add, delete, export, etc.

**Key Features:**

- Batch operations on selected entities
- Export to Excel and PDF
- Import from Excel
- Add new entity button

#### ExternalSelect.vue

A specialized select component for choosing related entities.

**Key Features:**

- Loads options from related entity types
- Displays meaningful entity names
- Handles dependencies between fields

#### CodeTypeSelect.vue

A specialized select component for code type values.

**Key Features:**

- Loads options from code types
- Displays code names
- Handles required/optional states

#### EntityTableCell.vue

Renders cell content based on field type with appropriate formatting.

**Key Features:**

- Type-specific rendering (date, currency, boolean, etc.)
- External entity reference display
- Truncation for long content
- Special formatting for URLs, emails, etc.

#### PaginationNav.vue

Provides pagination controls for entity tables.

**Key Features:**

- Page navigation
- Items per page selection
- Results count display

#### EntityTabComponent.vue

A variant of EntityComponent that uses tabs for organization.

**Key Features:**

- Similar to EntityComponent but with tab-based layout
- Useful for more complex entity management interfaces

#### FieldValueFilter.vue

A specialized component for selecting filter values based on field type.

**Key Features:**

- Adapts to field type (text, external reference, etc.)
- Searchable dropdown for values
- Handles null values

## Entity Page Service

### Overview

The `entityPage.ts` file provides a service class that handles the business logic for entity management components, particularly `EntityComponent.vue` and `EntityTabComponent.vue`. It serves as the controller layer between the UI components and the API services.

### EntityPage Class

The `EntityPage` class encapsulates the logic for managing entity CRUD operations, form state, and user interactions.

**Key Features:**

- Manages entity editing state (which entity is being edited)
- Handles form visibility and validation
- Provides methods for entity operations (add, edit, delete, toggle)
- Manages batch operations on selected entities
- Handles Excel import/export functionality
- Provides confirmation dialogs for destructive actions
- Manages error handling and notifications
- Maintains references to component instances (table, form)

### Integration with Components

The `EntityPage` service is instantiated in both `EntityComponent.vue` and `EntityTabComponent.vue` and serves as the bridge between the UI and the data layer:

1. It receives an API instance (`BuildApi`) during initialization
2. Component events (edit, delete, save) are delegated to `EntityPage` methods
3. `EntityPage` maintains the state of which entity is being edited
4. It handles the logic for showing/hiding the edit form modal
5. It provides methods for batch operations on selected entities

### Helper Functions

The module also provides helper functions:

- `bindContext`: Binds the context of specified methods to an `EntityPage` instance, ensuring proper `this` references when methods are passed as callbacks to components

## API Services

### API Overview

The `api` directory contains services that handle communication with backend APIs, providing a clean interface for CRUD operations and data management. These services are designed to work with the entity data model and integrate with Vue's reactivity system.

### Service Descriptions

#### api.ts

Defines the core API class that handles CRUD operations for entities.

**Key Features:**

- Generic implementation for any entity type
- Methods for add, update, delete operations
- List retrieval with filtering, sorting, and pagination
- Excel import/export functionality
- Date handling for API communication

#### useApi.ts

Provides a Vue-friendly wrapper around the API class with reactive properties.

**Key Features:**

- Reactive data properties (isLoading, entities, etc.)
- Automatic data fetching on filter/sort/page changes
- Simplified interface for component usage
- Handles loading states

#### requests.ts

Handles the actual HTTP requests to backend services.

**Key Features:**

- Authentication handling
- Multiple backend support
- Error handling
- Retry logic

#### baseQuery.ts

Defines the structure for API queries.

**Key Features:**

- Common query parameters
- Sorting definitions
- Filtering structures

#### entityResponse.ts

Defines the structure for API responses.

**Key Features:**

- Success/error information
- Entity data
- Pagination information
- Unique values for filtering

#### externUtils.ts

Module that handles external references and code look-ups

**Key Features:**

- Returns target entity types such as Employee, CodeItem, and Role
- Fetches a list of entities for a field to populare things like dropdowns

## Integration Examples

The entity-dataview components, EntityPage service, and API services work together to create a complete data management solution. Here's how they typically integrate:

1. A page component uses `EntityComponent` or `EntityTabComponent` to create a data management interface
2. The component initializes a `UseApi` instance for the specific entity type
3. An `EntityPage` instance is created with the API instance and handles business logic
4. The API service handles data fetching, filtering, and CRUD operations
5. The entity components render the UI and delegate actions to the EntityPage service
6. The EntityPage service coordinates with the API service to perform operations

This architecture provides a consistent, reusable approach to entity data management throughout the application.

Example usage:

```vue
<template>
  <EntityComponent type="Company" />
</template>

<script setup lang="ts">
import EntityComponent from '@/components/entity-dataview/EntityComponent.vue'
</script>
```

For more complex scenarios, the components can be customized through slots and props:

```vue
<template>
  <EntityComponent type="Invoice">
    <template #filter-right>
      <CustomFilterComponent />
    </template>
    <template #form-bottom>
      <InvoiceLineItems />
    </template>
  </EntityComponent>
</template>
```

This modular approach allows for consistent UI patterns while enabling customization for specific entity types and business requirements.

Note: When importing from Vuestic, always import 'vuestic-ui' and NOT 'vuestic-ui/web-components'

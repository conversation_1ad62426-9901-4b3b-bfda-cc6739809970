import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface CodeEntity extends TenantEntity {
  code?: string
}

export const CodeEntityInfo: Info<CodeEntity> = {
  typeName: 'CodeEntity',
  nameKey: 'id',
  sortKey: 'id',
  backend: 'BaseBiz',
  endpoint: 'Dummy',
  fields: {
    ...TenantEntityInfo.fields,
    code: { label: 'Code', type: 'smalltext', disabled: true },
  },
  options: {
    ...TenantEntityInfo.options,
  },
  default: {},
  columnsShown: new Set(),
  formLayout: [
    ['id', 'tenantId', 'code'],
    ['createAt', 'createBy'],
    ['updateAt', 'updateBy'],
  ],
}

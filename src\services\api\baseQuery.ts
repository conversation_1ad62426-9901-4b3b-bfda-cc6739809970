import { BaseEntity } from '../../data/entities/BaseEntity'

export interface BaseQuery {
  pageSize?: number
  pageIndex?: number
  idList?: string[]
  noList?: string[]
  dtoList?: BaseEntity[]
  fileList?: Record<string, File[]>
  filterString?: string
  fieldValues?: { [key: string]: string[] }
  uniqueFromField?: string
  isActive?: boolean
  sorts?: PageSort[]
}

export interface PageSort {
  sortField: string
  sortDirection: SortOperator
}

export enum SortOperator {
  asc = 0,
  desc = 1,
}

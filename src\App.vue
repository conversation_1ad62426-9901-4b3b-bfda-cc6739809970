<template>
  <RouterView v-slot="{ Component }">
    <Suspense>
      <Transition>
        <component :is="Component" />
      </Transition>
    </Suspense>
  </RouterView>
</template>

<style lang="scss">
@import 'scss/main.scss';

#app {
  font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 20rem;
}

.v-enter-active,
.v-leave-active {
  transition: opacity filter 2s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>

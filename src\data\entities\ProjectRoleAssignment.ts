import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface ProjectRoleAssignment extends TenantEntity {
  projectId: string
  roleCode: string
  employeeId: string
  hourlyCurrency: string
  hourlyRate?: number
  description?: string
}

export const projectRoleAssignmentInfo: Info<ProjectRoleAssignment> = {
  typeName: 'Project Role Assignment',
  typeShortName: 'Team Member',
  nameKey: 'roleCode',
  sortKey: 'roleCode',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'ProjectRoleAssignment',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    roleCode: { label: 'Role Code', type: 'external', required: true },
    employeeId: { label: 'Employee', type: 'external', required: true },
    hourlyCurrency: { label: 'Currency', type: 'codetype', required: true },
    hourlyRate: { label: 'Hourly Rate', type: 'currency' },
    description: { label: 'Description', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    hourlyCurrency: { typeCode: 'Currency' },
    hourlyRate: { currencyKey: 'hourlyCurrency' },
    roleCode: { entity: 'Role', constraints: { usage: ['Project', 'Both'] } },
    employeeId: { entity: 'Employee' },
    projectId: { entity: 'Project' },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    roleCode: '',
    employeeId: '',
    hourlyCurrency: 'CAD',
  },
  columnsShown: new Set(['roleCode', 'employeeId', 'hourlyRate', 'hourlyCurrency']),
  formLayout: [['employeeId', 'roleCode'], ['hourlyRate', 'hourlyCurrency'], ['description']],
}

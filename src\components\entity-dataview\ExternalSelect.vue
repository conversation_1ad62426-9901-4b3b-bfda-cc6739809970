<script setup lang="ts">
import { computed, ref, Ref, watch } from 'vue'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { trunc, validators } from '../../services/utils'
import { schema, SchemaKey } from '../../data/schema'
import { getExternArray } from '../../services/externUtils'
import { BaseKey, ExternalOp, Key } from '../../data/FieldTypes'
import { getEntityName } from '../../services/externUtils'

const props = defineProps<{
  fromType: SchemaKey // local type
  fromKey: string // for the local entity storing the external identifier
  option: any // external type
  isCode?: boolean // no as in projectNo (will always be "code")
  required?: boolean
  disabled?: boolean
  inline?: boolean
}>()

const newEntity = defineModel({ required: true }) as Ref<BaseEntity>
const selected = defineModel('selected') as Ref<BaseEntity>

const _keyName = props.fromKey as Key<BaseEntity>
const _option = props.option as ExternalOp<BaseEntity>
const field = schema[props.fromType].fields[_keyName]

const items = ref(await getExternArray(props.fromType, props.fromKey, newEntity.value))

const label = props.inline ? '' : field.label + (field.type !== 'bool' && field.required ? ' *' : '')
const toKey = (_option.valueKey ?? (props.isCode ? 'code' : 'id')) as BaseKey

const autofill = () => {
  // autofill first item
  if (!newEntity.value[_keyName] && props.required && items.value[0]) {
    ;(newEntity.value[_keyName] as string) = (items.value[0][toKey] as string) ?? ''
  }
}
autofill()

const dynamicTriggers = computed(() => {
  const keys = [
    ...(_option.joins ? Object.keys(_option.joins) : []),
    ...(_option.extraInfo ? Object.values(_option.extraInfo) : []),
  ]
  return Object.fromEntries(keys.map((key) => [key, newEntity.value[key as BaseKey]])) as Record<string, any>
})

watch(
  dynamicTriggers,
  async () => {
    console.log('Triggering fetch with fields:', {
      ...(_option.extraInfo
        ? Object.fromEntries(
            Object.entries(_option.extraInfo).map(([remoteField, localField]) => [
              remoteField,
              (newEntity.value as any)[localField],
            ]),
          )
        : {}),
    })
    newEntity.value[_keyName] = schema[props.fromType].default[_keyName]
    items.value = await getExternArray(props.fromType, props.fromKey, newEntity.value)
    autofill()
  },
  { deep: true },
)
</script>
<template>
  <VaSelect
    v-model="newEntity[_keyName]"
    :label="label"
    :options="items"
    :text-by="(x: BaseEntity) => trunc(getEntityName(x, _option.entity), 30)"
    :value-by="
      (x) => {
        selected = x as BaseEntity
        return (x as BaseEntity)?.[toKey]
      }
    "
    :rules="required ? [validators.required] : []"
    :name="fromKey"
    :clearable="!required"
    :searchable="items.length >= 3"
    :disabled="disabled"
  />
</template>

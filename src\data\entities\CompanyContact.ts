import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'

export interface CompanyContact extends TenantEntity {
  companyId: string
  name: string
  jobTitle?: string
  description?: string
  phone?: string
  mobile?: string
  email?: string
  isActive: boolean
  fax?: string
  address1?: string
  address2?: string
  city?: string
  province?: string
  postalCode?: string
}

export const companyContactInfo: Info<CompanyContact> = {
  typeName: 'Company Contact',
  nameKey: 'name',
  sortKey: 'name',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'Contact',
  fields: {
    companyId: { label: 'Company', type: 'external', required: true },
    name: { label: 'Name', type: 'bigtext', required: true },
    jobTitle: { label: 'Title', type: 'bigtext' },
    phone: { label: 'Phone', type: 'tel' },
    mobile: { label: 'Mobile', type: 'smalltext' },
    email: { label: 'Email', type: 'email' },
    isActive: { label: 'Active', type: 'bool', required: true },
    fax: { label: 'Fax', type: 'tel' },
    address1: { label: 'Address', type: 'place', required: true },
    address2: { label: 'Address 2', type: 'place' },
    city: { label: 'City', type: 'place' },
    province: { label: 'Province', type: 'codetype' },
    postalCode: { label: 'Postal Code', type: 'place' },
    description: { label: 'Description', type: 'textarea' },
    ...TenantEntityInfo.fields,
  },
  options: {
    companyId: { entity: 'Company' },
    province: { typeCode: 'Province' },
    ...TenantEntityInfo.options,
  },
  default: {
    companyId: '',
    name: '',
    isActive: true,
  },
  columnsShown: new Set(['companyId', 'name', 'jobTitle', 'phone', 'email', 'isActive']),
  formLayout: [
    ['companyId', 'name'],
    ['jobTitle', 'email'],
    ['phone', 'mobile', 'fax'],
    ['address1'],
    ['address2'],
    ['city', 'province', 'postalCode'],
    ['description'],
    ['isActive'],
  ],
}

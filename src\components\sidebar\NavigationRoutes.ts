export interface INavigationRoute {
  name: string
  displayName: string
  meta: { icon: string }
  children?: INavigationRoute[]
}

export default {
  root: {
    name: '/',
    displayName: 'navigationRoutes.home',
  },
  routes: [
    {
      name: 'dashboard',
      displayName: 'menu.dashboard',
      meta: {
        icon: 'vuestic-iconset-dashboard',
      },
    },
    {
      name: 'global-admin',
      displayName: 'menu.global-admin',
      meta: {
        icon: 'public',
      },
      children: [
        {
          name: 'manage-tenant',
          displayName: 'menu.manage-tenant',
        },
        {
          name: 'tenant-admins',
          displayName: 'menu.tenant-admin-account',
        },
        {
          name: 'global-admin-account',
          displayName: 'menu.global-admin-account',
        },
        {
          name: 'normal-user-account',
          displayName: 'menu.normal-user-account',
        },
        {
          name: 'access-resource',
          displayName: 'menu.access-resource',
        },
      ],
    },
    {
      name: 'tenant-admin',
      displayName: 'menu.tenant-admin',
      meta: {
        icon: 'account_tree',
      },
      children: [
        {
          name: 'tenant-settings',
          displayName: 'menu.tenant-settings',
        },
        {
          name: 'manage-company',
          displayName: 'menu.manage-company',
        },
      ],
    },
    {
      name: 'project-management',
      displayName: 'menu.project',
      meta: {
        icon: 'inventory_2',
      },
      children: [
        {
          name: 'manage-project',
          displayName: 'menu.manage-project',
        },
        {
          name: 'manage-contract',
          displayName: 'menu.manage-contract',
        },
        {
          name: 'manage-task',
          displayName: 'menu.manage-task',
        },
        {
          name: 'manage-document',
          displayName: 'menu.manage-document',
        },
      ],
    },
    {
      name: 'ticket-management',
      displayName: 'menu.ticket',
      meta: {
        icon: 'style',
      },
      children: [
        {
          name: 'manage-ticket',
          displayName: 'menu.manage-ticket',
        },
        {
          name: 'shopping-list',
          displayName: 'menu.shopping-list',
        },
        {
          name: 'manage-time-log',
          displayName: 'menu.manage-time-log',
        },
        {
          name: 'manage-invoice',
          displayName: 'menu.manage-invoice',
        },
        {
          name: 'invoice-queue',
          displayName: 'menu.invoice-queue',
        },
      ],
    },
    {
      name: 'product-management',
      displayName: 'menu.product',
      meta: {
        icon: 'shopping_cart',
      },
      children: [
        {
          name: 'manage-product',
          displayName: 'menu.manage-product',
        },
        {
          name: 'release-tracking',
          displayName: 'menu.release-tracking',
        },
        {
          name: 'license-tracking',
          displayName: 'menu.license-tracking',
        },
        {
          name: 'deployment-tracking',
          displayName: 'menu.deployment-tracking',
        },
      ],
    },
  ] as INavigationRoute[],
}

import { calcMinutesWorked } from '../../services/dateTimeUtils'
import { Info } from '../FieldTypes'
import { BaseEntity } from './BaseEntity'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { Ticket } from './Ticket'

export interface ProjectTimeLog extends TenantEntity {
  projectId: string
  ticketId?: string
  employeeId: string
  startDate: Date
  endDate: Date
  taskType: string
  comment?: string
  workMinutes: number
  factor: number
  adjustTime?: number
  invoiceId?: string
  isAdminEntry: boolean
  preSelectFlag: boolean
}

export const TaskTypes = ['Analysis', 'Development', 'DBA', 'Testing', 'Deployment', 'PeerSupport', 'Other'] as const
export type TaskType = (typeof TaskTypes)[number]

export const projectTimeLogInfo: Info<ProjectTimeLog> = {
  typeName: 'Project Time Log',
  typeShortName: 'Time Log',
  nameKey: 'taskType',
  sortKey: 'startDate',
  backend: 'Project',
  endpoint: 'TimeLog',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    ticketId: { label: 'Ticket', type: 'external' },
    taskType: { label: 'Task', type: 'codetype', required: true },
    employeeId: { label: 'Employee', type: 'external', required: true },
    startDate: { label: 'Start Time', type: 'datetime', required: true },
    endDate: { label: 'End Time', type: 'datetime', required: true },
    workMinutes: { label: 'Work Time', type: 'time', required: true },
    comment: { label: 'Note', type: 'textarea' },
    factor: { label: 'Factor', type: 'percent', required: true },
    adjustTime: { label: 'Billable Time Offset (±)', type: 'time' },
    invoiceId: { label: 'Invoice', type: 'external' },
    isAdminEntry: { label: 'Admin Entry', type: 'bool', required: true },
    preSelectFlag: { label: 'Pre-Select', type: 'bool', required: true },
    ...TenantEntityInfo.fields,
  },
  options: {
    projectId: { entity: 'Project' },
    ticketId: { entity: 'Ticket', joins: { projectId: 'projectId' } },
    taskType: { typeCode: 'TimeLogTask' },
    employeeId: { entity: 'Employee' },
    invoiceId: { entity: 'Invoice', sync: (x) => (x.value.preSelectFlag = false) },
    startDate: { sync: (x) => (x.value.workMinutes = calcMinutesWorked(x.value.startDate, x.value.endDate)) },
    endDate: { sync: (x) => (x.value.workMinutes = calcMinutesWorked(x.value.startDate, x.value.endDate)) },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    employeeId: '',
    startDate: new Date(),
    endDate: new Date(),
    taskType: '',
    workMinutes: 0,
    factor: 100,
    isAdminEntry: false,
    preSelectFlag: false,
  },
  columnsShown: new Set(['projectId', 'ticketId', 'taskType', 'employeeId', 'startDate', 'endDate', 'workMinutes']),
  formLayout: [
    ['projectId', 'ticketId'],
    ['taskType', 'employeeId'],
    ['startDate', 'endDate'],
    ['workMinutes', 'adjustTime'],
    ['invoiceId', 'preSelectFlag'],
    ['comment'],
  ],
  defaultFilter: 'preSelectFlag',
  highlight: (x: ProjectTimeLog) => x.preSelectFlag,
  lowlight: (x: ProjectTimeLog, extList?: Record<string, Record<string, BaseEntity>>) =>
    x.ticketId ? !(extList?.['ticketId']?.[x.ticketId] as Ticket)?.billable : false,
  toggleKey: 'preSelectFlag',
  toggle: (x: ProjectTimeLog) => (x.preSelectFlag = !x.preSelectFlag),
}

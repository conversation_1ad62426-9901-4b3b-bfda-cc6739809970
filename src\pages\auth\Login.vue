<template>
  <VaForm ref="form" @submit.prevent="submitLogin">
    <h1 class="font-semibold text-4xl mb-4">Log in</h1>
    <p v-if="!chooseTenantMode" class="text-base mb-4 leading-5">
      New to OneTeam?
      <RouterLink :to="{ name: 'signup' }" class="font-semibold text-primary">Sign up</RouterLink>
    </p>
    <VaInput
      v-if="!chooseTenantMode"
      v-model="formData.username"
      :rules="[validators.required]"
      class="mb-4"
      label="Username"
      type="text"
      placeholder="Enter username..."
      :disabled="chooseTenantMode"
    />
    <VaValue v-slot="isPasswordVisible" :default-value="false">
      <VaInput
        v-if="!chooseTenantMode"
        v-model="formData.password"
        :rules="[validators.required]"
        :type="isPasswordVisible.value ? 'text' : 'password'"
        class="mb-4"
        label="Password"
        placeholder="Enter Password..."
        :disabled="chooseTenantMode"
        @clickAppendInner.stop="isPasswordVisible.value = !isPasswordVisible.value"
      >
        <template #appendInner>
          <VaIcon
            :name="isPasswordVisible.value ? 'mso-visibility_off' : 'mso-visibility'"
            class="cursor-pointer"
            color="secondary"
          />
        </template>
      </VaInput>
    </VaValue>

    <div
      v-if="!chooseTenantMode"
      class="auth-layout__options flex flex-col sm:flex-row items-start sm:items-center justify-between"
    >
      <RouterLink :to="{ name: 'reset-password' }" class="mt-2 sm:mt-0 sm:ml-1 font-semibold text-primary">
        Forgot password?
      </RouterLink>
    </div>

    <VaSelect
      v-if="chooseTenantMode"
      v-model="tenant"
      :rules="[validators.required]"
      :options="tenantList"
      label="Choose Tenant"
      text-by="name"
      value-by="id"
      class="mb-4"
    />

    <div class="flex justify-center mt-4">
      <VaButton v-if="!chooseTenantMode" class="w-full" @click="submitLogin"> Login</VaButton>
      <VaButton v-else class="w-full" @click="chooseTenant"> Continue</VaButton>
    </div>
  </VaForm>
</template>

<script lang="ts" setup>
import { reactive, Ref, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm, useToast } from 'vuestic-ui'
import { validators } from '../../services/utils'
import { useUserStore } from '../../stores/user-store'
import { authedRequest } from '../../services/api/requests'
import { Tenant } from '../../data/entities/Tenant'

const { validate } = useForm('form')
const { push } = useRouter()
const { init } = useToast()

const formData = reactive({
  username: '',
  password: '',
})
const chooseTenantMode = ref(false)
const store = useUserStore()
const tenant = ref() as Ref<string>
const tenantList = ref([]) as Ref<Tenant[]>

const submitLogin = async () => {
  if (!validate()) return

  const success = await store.login(formData.username, formData.password)
  if (!success) {
    init({ message: 'Login failed', color: 'danger' })
    return
  }

  //  No need to pass userId in query — the JWT handles it
  const res = await authedRequest('BaseBiz', '/UserTenantList', 'GET')
  tenantList.value = res.entity as Tenant[]

  if (tenantList.value.length === 0) {
    init({ message: 'User has no tenants!', color: 'danger' })
  } else if (tenantList.value.length === 1) {
    await store.setTenant(tenant.value!)
    loginSuccess()
  } else {
    chooseTenantMode.value = true
    tenant.value = tenantList.value[0].id!
  }
}

const chooseTenant = async () => {
  if (!validate()) {
    return
  }
  await authedRequest('BaseBiz', '/SetTenant', 'POST', { id: tenant.value } as any)
  loginSuccess()
}

const loginSuccess = () => {
  init({ message: "You've successfully logged in", color: 'success' })
  push({ name: 'dashboard' })
}
</script>

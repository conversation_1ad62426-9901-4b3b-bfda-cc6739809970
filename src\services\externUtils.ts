/**
 * @fileoverview Utilities for handling external references and code lookups in the application.
 * This module provides functions for retrieving, mapping, and displaying data from related entities.
 * It handles three types of external references: 'code', 'external', and 'codetype'.
 */

import { BaseEntity } from '../data/entities/BaseEntity'
import { CodeEntity } from '../data/entities/CodeEntity'
import { CodeItem } from '../data/entities/CodeItem'
import { CodeType } from '../data/entities/CodeType'
import { BaseKey, CodeTypeOp, ExternalOp, FilteredOp } from '../data/FieldTypes'
import { schema, SchemaKey } from '../data/schema'
import { BaseQuery } from './api/baseQuery'
import { authedRequest } from './api/requests'

// "external" is a subset of "extern" !!!

/**
 * The supported external reference types in the system.
 * - 'code': References to CodeEntity objects via their code field
 * - 'external': Direct references to other entities via their ID
 * - 'codetype': References to CodeItem objects via a CodeType
 */
export const externTypes = ['code', 'external', 'codetype'] as string[]

/**
 * Determines if a field is an external reference type.
 *
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} field - The field name to check
 * @returns {boolean} True if the field is an extern type ('code', 'external', or 'codetype')
 */
export const isExtern = (objType: SchemaKey, field: string): boolean => {
  const columnType = schema[objType].fields[field].type
  return externTypes.includes(columnType)
}

/**
 * Gets the schema key for an external reference.
 *
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @returns {SchemaKey} The schema key of the referenced entity
 */
export const getExternalSchema = (objType: SchemaKey, fromKey: string): SchemaKey => {
  return (schema[objType].options[fromKey] as ExternalOp<BaseEntity>).entity
}

/**
 * Gets the schema key for any extern type (including 'codetype').
 *
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @returns {SchemaKey} The schema key of the referenced entity (returns 'CodeItem' for 'codetype' fields)
 */
export const getExternSchema = (objType: SchemaKey, fromKey: string): SchemaKey => {
  const columnType = schema[objType].fields[fromKey].type
  if (columnType === 'codetype') {
    return 'CodeItem'
  }
  return getExternalSchema(objType, fromKey)
}

/**
 * Retrieves an array of external entities based on the specified criteria.
 * Used by externalSelect components and other functions that need to fetch related entities.
 *
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @param {BaseEntity} [joinEntity] - Optional entity to join with for filtering
 * @param {string[]} [codeList] - Optional list of codes to filter by
 * @returns {Promise<BaseEntity[]>} Promise resolving to an array of external entities
 */
export const getExternArray = async (
  objType: SchemaKey,
  fromKey: string,
  joinEntity?: BaseEntity,
  codeList?: string[],
): Promise<BaseEntity[]> => {
  const option = schema[objType].options[fromKey] as ExternalOp<BaseEntity>
  const fieldType = schema[objType].fields[fromKey].type

  let extInfo
  if (fieldType === 'filtered') {
    extInfo = {
      ...schema[(option as FilteredOp<BaseEntity>).entity],
      endpoint: (option as FilteredOp<BaseEntity>).filteredEndpoint,
    }
  } else {
    extInfo = schema[(option as ExternalOp<BaseEntity>).entity]
  }
  const request = { fieldValues: {} } as BaseQuery
  if (joinEntity && option.joins) {
    // if request needs to match a field with a corresponding field on the extern
    request.fieldValues = Object.fromEntries(
      // each field will have one related value only!
      Object.entries(option.joins).map(([key, value]) => [value, [joinEntity[key as BaseKey]]]),
    ) as Record<string, string[]>
  }
  if (option.constraints) {
    request.fieldValues = {
      ...request.fieldValues,
      ...option.constraints!,
    }
  }
  if (joinEntity && option.extraInfo) {
    request.fieldValues ??= {}
    for (const [remoteField, localField] of Object.entries(option.extraInfo)) {
      const value = (joinEntity as any)[localField]
      if (typeof value === 'string') {
        request.fieldValues[remoteField] = [value]
      }
    }
  }
  if (codeList) {
    request.noList = codeList
  }
  const url = fieldType === 'filtered' ? '/' + extInfo.endpoint : '/' + extInfo.endpoint + 'List'

  return (await applyTrueNames(
    objType,
    fromKey,
    (await authedRequest(extInfo.backend, url, 'POST', request)).entity,
  )) as BaseEntity[]
}

/**
 * Retrieves code items for a specific code type.
 *
 * @param {string} typeCode - The code type to retrieve items for
 * @param {string[]} [valueList] - Optional list of values to filter by
 * @returns {Promise<CodeItem[]>} Promise resolving to an array of CodeItem entities
 */
export const getCodeItemArray = async (typeCode: string, valueList?: string[]): Promise<CodeItem[]> => {
  const type = (
    (await authedRequest('BaseBiz', '/CodeTypeByTypeCode?typeCode=' + typeCode, 'GET')) as { entity: CodeType }
  ).entity
  const request = {
    fieldValues: {
      codeTypeId: [type.id!],
    },
  } as BaseQuery
  if (valueList) {
    request.fieldValues!['value'] = valueList
  }
  return (await authedRequest('BaseBiz', '/CodeItemList', 'POST', request)).entity
}

/**
 * Retrieves a map of external entities based on the specified criteria.
 * Handles different extern types (code, external, codetype) appropriately.
 *
 * @param {string[]} queryList - List of IDs to query
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @returns {Promise<Record<string, BaseEntity>>} Promise resolving to a map of ID to entity
 * @throws {Error} If the field type is invalid
 */
export const getExternMap = async (
  queryList: string[],
  objType: SchemaKey,
  fromKey: string,
): Promise<Record<string, BaseEntity>> => {
  if (queryList.length === 0) {
    return {} as Record<string, BaseEntity>
  }
  const fieldType = schema[objType].fields[fromKey].type
  if (fieldType === 'codetype') {
    const valueMap = await getCodeItemMap(queryList, objType, fromKey)
    return valueMap
  } else if (fieldType === 'external' || fieldType === 'filtered') {
    const idMap = await getIdExternalMap(queryList, objType, fromKey)
    return (await applyTrueNames(objType, fromKey, idMap)) as Record<string, BaseEntity>
  } else if (fieldType === 'code') {
    const valueMap = await getCodeMap(queryList, objType, fromKey)
    return (await applyTrueNames(objType, fromKey, valueMap)) as Record<string, BaseEntity>
  }
  throw new Error('Invalid field type')
}

/**
 * Retrieves a map of external entities by ID.
 * Used internally by getExternMap.
 *
 * @param {string[]} queryIdList - List of IDs to query
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @returns {Promise<Record<string, BaseEntity>>} Promise resolving to a map of ID to entity
 * @private
 */
const getIdExternalMap = async (
  queryIdList: string[],
  objType: SchemaKey,
  fromKey: string,
): Promise<Record<string, BaseEntity>> => {
  if (queryIdList.length === 0) {
    return {}
  }
  const external = schema[getExternSchema(objType, fromKey)]
  return Object.fromEntries(
    (
      await authedRequest(external.backend, '/' + external.endpoint + 'List', 'POST', {
        idList: queryIdList,
      })
    ).entity.map((x: BaseEntity) => [x.id, x]),
  ) as Record<string, BaseEntity>
}

/**
 * Retrieves a map of code items.
 * Used internally by getExternMap for 'codetype' fields.
 *
 * @param {string[]} queryList - List of IDs to query
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @returns {Promise<Record<string, CodeItem>>} Promise resolving to a map of ID to CodeItem
 * @private
 */
const getCodeItemMap = async (
  queryList: string[],
  objType: SchemaKey,
  fromKey: string,
): Promise<Record<string, CodeItem>> => {
  if (queryList.length === 0) {
    return {}
  }
  const typeCode = (schema[objType].options[fromKey] as CodeTypeOp).typeCode
  const items = await getCodeItemArray(typeCode, queryList)
  return Object.fromEntries(items.map((x: CodeItem) => [x.value, x]))
}

/**
 * Retrieves a map of code entities.
 * Used internally by getExternMap for 'code' fields.
 *
 * @param {string[]} queryList - List of IDs to query
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @returns {Promise<Record<string, CodeItem>>} Promise resolving to a map of code to entity
 * @private
 */
const getCodeMap = async (
  queryList: string[],
  objType: SchemaKey,
  fromKey: string,
): Promise<Record<string, CodeItem>> => {
  if (queryList.length === 0) {
    return {}
  }
  const codeArr = await getExternArray(objType, fromKey, undefined, queryList)
  const codeMap = Object.fromEntries(codeArr.map((x) => [(x as CodeEntity).code, x])) as Record<string, CodeItem>
  return codeMap
}

/**
 * Gets the display name for an entity, using trueName if available.
 *
 * @param {BaseEntity} entity - The entity to get the name for
 * @param {SchemaKey} objType - The schema key of the entity type
 * @returns {string|undefined} The display name of the entity
 */
export const getEntityName = (entity: BaseEntity, objType: SchemaKey): string | undefined => {
  const trueName = (entity as any).trueName as string | undefined
  if (trueName) return trueName

  const nameKey = schema[objType].nameKey as BaseKey
  const val = entity[nameKey]
  return typeof val === 'string' ? val : val?.toString()
}

/**
 * Applies "true names" to a collection of entities.
 * This resolves nested external references to get the actual display name of entities.
 *
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string} fromKey - The field name containing the external reference
 * @param {BaseEntity[]|Record<string, BaseEntity>} entities - Array or record of entities to process
 * @returns {Promise<BaseEntity[]|Record<string, BaseEntity>>} Promise resolving to the entities with true names applied
 */
export const applyTrueNames = async (
  objType: SchemaKey,
  fromKey: string,
  entities: BaseEntity[] | Record<string, BaseEntity>,
): Promise<BaseEntity[] | Record<string, BaseEntity>> => {
  const toType = getExternSchema(objType, fromKey)
  // if (schema[toType].fields[schema[toType].nameKey].type !== 'external') {
  //   return entities
  // }
  const trueNames = await getTrueNames(
    toType,
    Object.values(entities).map((x) => x[schema[toType].nameKey as BaseKey] as string),
  )
  // console.log(trueNames)
  const entityValues = Object.values(entities)
  for (let i = 0; i < trueNames.length; i++) {
    ;(entityValues[i] as any).trueName = trueNames[i]
  }
  // console.log(entities)
  return entities
}

/**
 * Recursively resolves true names for a list of IDs.
 * Used internally by applyTrueNames.
 *
 * @param {SchemaKey} objType - The schema key of the object type
 * @param {string[]} idList - List of IDs to resolve names for
 * @returns {Promise<string[]>} Promise resolving to a list of true names
 * @private
 */
const getTrueNames = async (objType: SchemaKey, idList: string[]): Promise<string[]> => {
  // console.log(type)
  // console.log(idList)
  const nameKey = schema[objType].nameKey as BaseKey
  if (schema[objType].fields[nameKey].type !== 'external') {
    return idList
  }
  if (idList.length === 0) {
    return idList
  }
  const extType = getExternalSchema(objType, nameKey)
  const extKey = schema[extType].nameKey as BaseKey
  if (objType === extType) {
    return idList
  }
  const idMap = await getIdExternalMap(idList, objType, nameKey)
  // console.log(idMap)
  return getTrueNames(
    extType,
    idList.map((id) => idMap[id][extKey] as string),
  )
}

import { accessResourceInfo } from './entities/AccessResource'
import { assignableRoleInfo } from './entities/AssignableRole'
import { BaseEntityInfo } from './entities/BaseEntity'
import { codeItemInfo } from './entities/CodeItem'
import { codeItemAttributeInfo } from './entities/CodeItemAttribute'
import { codeTypeInfo } from './entities/CodeType'
import { companyInfo } from './entities/Company'
import { companyContactInfo } from './entities/CompanyContact'
import { employeeInfo } from './entities/Employee'
import { globalAdminInfo } from './entities/GlobalAdmin'
import { notificationTemplateInfo } from './entities/NotificationTemplate'
import { i18nKeyInfo } from './entities/I18nKey'
import { i18nTranslationInfo } from './entities/I18nTranslation'
import { invoiceInfo } from './entities/Invoice'
import { invoiceItemInfo } from './entities/InvoiceItem'
import { organizationInfo } from './entities/Organization'
import { organizationEmployeeInfo } from './entities/OrganizationEmployee'
import { employeeListByOrganizationInfo } from './entities/OrganizationEmployeeList'
import { organizationHierarchyInfo } from './entities/OrganizationHierarchy'
import { productInfo } from './entities/Product'
import { productReleaseTrackingInfo } from './entities/ProductReleaseTracking'
import { productLicenseTrackingInfo } from './entities/ProductLicenseTracking'
import { productDeploymentTrackingInfo } from './entities/ProductDeploymentTracking'
import { projectInfo } from './entities/Project'
import { projectContractInfo } from './entities/ProjectContract'
import { projectDocumentInfo } from './entities/ProjectDocument'
import { projectRelatedPartyInfo } from './entities/ProjectRelatedParty'
import { projectRoleAssignmentInfo } from './entities/ProjectRoleAssignment'
import { projectTimeLogInfo } from './entities/ProjectTimeLog'
import { projectTaskInfo } from './entities/ProjectTask'
import { projectPaymentPlanInfo } from './entities/ProjectPaymentPlan'
import { projectPaymentFactInfo } from './entities/ProjectPaymentFact'
import { roleInfo } from './entities/Role'
import { roleAccessInfo } from './entities/RoleAccess'
import { roleAssignmentInfo } from './entities/RoleAssignment'
import { sequenceNoInfo } from './entities/SequenceNo'
import { tenantInfo } from './entities/Tenant'
import { ticketInfo } from './entities/Ticket'
import { ticketBillingInfo } from './entities/TicketBilling'
import { ticketBillingDeliveryInfo } from './entities/TicketBillingDelivery'
import { ticketBillingPaymentInfo } from './entities/TicketBillingPayment'
import { ticketDevOpsLinkInfo } from './entities/TicketDevOpsLink'
import { ticketDiscussionInfo } from './entities/TicketDiscussion'
import { ticketLinkInfo } from './entities/TicketLink'
import { ticketReviewInfo } from './entities/TicketReview'
import { userAccountInfo } from './entities/UserAccount'
import { Info } from './FieldTypes'

const schemaKeys = [
  'BaseEntity',
  'AccessResource',
  'AssignableRole',
  'CodeItem',
  'CodeItemAttribute',
  'CodeType',
  'Company',
  'CompanyContact',
  'Employee',
  'EmployeeListByOrganization',
  'GlobalAdmin',
  'NotificationTemplate',
  'Organization',
  'OrganizationEmployee',
  'OrganizationHierarchy',
  'Product',
  'ProductReleaseTracking',
  'ProductLicenseTracking',
  'ProductDeploymentTracking',
  'ProjectContract',
  'ProjectRelatedParty',
  'ProjectPaymentPlan',
  'ProjectPaymentFact',
  'ProjectRoleAssignment',
  'ProjectTimeLog',
  'ProjectTask',
  'Project',
  'Role',
  'RoleAccess',
  'RoleAssignment',
  'SequenceNo',
  'Tenant',
  'Ticket',
  'TicketBilling',
  'TicketBillingDelivery',
  'TicketBillingPayment',
  'TicketDevOpsLink',
  'TicketDiscussion',
  'TicketLink',
  'TicketReview',
  'UserAccount',
  'I18nKey',
  'I18nTranslation',
  'Invoice',
  'InvoiceItem',
  'ProjectDocument',
] as const
export type SchemaKey = (typeof schemaKeys)[number]

// I don't think my computer is strong enough for this
// export type KeyOf<T> = T extends T ? keyof T : never
// export type All =
//   | BaseEntity
//   | AccessResource
//   | AssignableRole
//   | CodeItem
//   | CodeItemAttribute
//   | CodeType
//   | Company
//   | CompanyContact
//   | Employee
//   | Organization
//   | OrganizationEmployee
//   | OrganizationHierarchy
//   | ProjectContract
//   | ProjectRelatedParty
//   | ProjectRoleAssignment
//   | ProjectTimeLog
//   | ProjectTask
//   | Project
//   | Role
//   | RoleAccess
//   | RoleAssignment
//   | SequenceNo
//   | Tenant
//   | Ticket
//   | TicketBilling
//   | TicketBillingDelivery
//   | TicketBillingPayment
//   | TicketDevOpsLink
//   | TicketDiscussion
//   | TicketLink
//   | TicketReview
//   | UserAccount

export const schema: Record<SchemaKey, Info<any>> = {
  BaseEntity: BaseEntityInfo,
  AccessResource: accessResourceInfo,
  AssignableRole: assignableRoleInfo,
  CodeItem: codeItemInfo,
  CodeItemAttribute: codeItemAttributeInfo,
  CodeType: codeTypeInfo,
  Company: companyInfo,
  CompanyContact: companyContactInfo,
  Employee: employeeInfo,
  EmployeeListByOrganization: employeeListByOrganizationInfo,
  GlobalAdmin: globalAdminInfo,
  NotificationTemplate: notificationTemplateInfo,
  Organization: organizationInfo,
  OrganizationEmployee: organizationEmployeeInfo,
  OrganizationHierarchy: organizationHierarchyInfo,
  Product: productInfo,
  ProductReleaseTracking: productReleaseTrackingInfo,
  ProductLicenseTracking: productLicenseTrackingInfo,
  ProductDeploymentTracking: productDeploymentTrackingInfo,
  ProjectContract: projectContractInfo,
  ProjectRelatedParty: projectRelatedPartyInfo,
  ProjectRoleAssignment: projectRoleAssignmentInfo,
  ProjectTimeLog: projectTimeLogInfo,
  ProjectTask: projectTaskInfo,
  ProjectPaymentPlan: projectPaymentPlanInfo,
  ProjectPaymentFact: projectPaymentFactInfo,
  Project: projectInfo,
  Role: roleInfo,
  RoleAccess: roleAccessInfo,
  RoleAssignment: roleAssignmentInfo,
  SequenceNo: sequenceNoInfo,
  Tenant: tenantInfo,
  Ticket: ticketInfo,
  TicketBilling: ticketBillingInfo,
  TicketBillingDelivery: ticketBillingDeliveryInfo,
  TicketBillingPayment: ticketBillingPaymentInfo,
  TicketDevOpsLink: ticketDevOpsLinkInfo,
  TicketDiscussion: ticketDiscussionInfo,
  TicketLink: ticketLinkInfo,
  TicketReview: ticketReviewInfo,
  UserAccount: userAccountInfo,
  I18nKey: i18nKeyInfo,
  I18nTranslation: i18nTranslationInfo,
  Invoice: invoiceInfo,
  InvoiceItem: invoiceItemInfo,
  ProjectDocument: projectDocumentInfo,
} as const

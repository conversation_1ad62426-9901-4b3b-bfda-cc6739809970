import { Info } from '../FieldTypes'
import { CodeEntity } from './CodeEntity'
import { CompanyContact } from './CompanyContact'
import { TenantEntityInfo } from './TenantEntity'

export interface Company extends CodeEntity {
  name: string
  description?: string
  isActive: boolean
  phone?: string
  fax?: string
  address1?: string
  address2?: string
  city?: string
  province?: string
  postalCode?: string
  webSite?: string
  alias?: string
  currency?: string
  companyContacts?: CompanyContact[]
}

export const companyInfo: Info<Company> = {
  typeName: 'Company',
  nameKey: 'name',
  sortKey: 'code',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'Company',
  fields: {
    code: { label: 'Code', type: 'smalltext', disabled: true },
    name: { label: 'Company Name', type: 'bigtext', required: true },
    alias: { label: 'Alias', type: 'smalltext' },
    phone: { label: 'Phone', type: 'tel' },
    fax: { label: 'Fax', type: 'tel' },
    currency: { label: 'Currency', type: 'codetype' },
    address1: { label: 'Address', type: 'place', required: true },
    address2: { label: 'Address Second Line', type: 'place' },
    city: { label: 'City', type: 'place' },
    province: { label: 'Province', type: 'codetype' },
    postalCode: { label: 'Postal Code', type: 'place' },
    webSite: { label: 'Website', type: 'url' },
    description: { label: 'Description', type: 'textarea' },
    isActive: { label: 'Active', type: 'bool', required: true },
    companyContacts: { label: 'Contacts', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    currency: { typeCode: 'Currency' },
    province: { typeCode: 'Province' },
    companyContacts: { entity: 'CompanyContact' },
    ...TenantEntityInfo.options,
  },
  default: {
    name: '',
    code: 'Auto-generated',
    currency: 'CAD',
    isActive: true,
  },
  columnsShown: new Set(['code', 'name', 'alias', 'phone', 'isActive']),
  formLayout: [
    ['code', 'name', 'alias'],
    ['webSite', 'phone', 'fax'],
    ['address1', 'address2', 'postalCode'],
    ['city', 'province', 'currency'],
    ['isActive'],
    ['description'],
  ],
  tabList: {
    CompanyContact: { tabName: 'Contacts', relation: { id: 'companyId' } },
  },
}

<script setup lang="ts">
import { defineVaDataTableColumns } from 'vuestic-ui'
import ProjectBadges from '../../../../pages/project-management/projects-overview/widgets/ProjectBadges.vue'
import { ref } from 'vue'
import { Pagination } from '../../../../services/api/api'
import { Project } from '../../../../data/entities/Project'
import { UseApi } from '../../../../services/api/useApi'

const columns = defineVaDataTableColumns([
  { label: 'Name', key: 'name', sortable: true },
  { label: 'Main type', key: 'mainType', sortable: true },
  { label: 'Status', key: 'status', sortable: true },
])

const pagination = ref<Pagination>({ page: 1, perPage: 8, total: 0 })
const {
  objs: projects,
  isLoading,
  sorting,
} = await new UseApi<Project>('Project').build({
  pagination,
})

// const avatarColor = (userName: string) => {
//   const colors = ['primary', '#FFD43A', '#ADFF00', '#262824', 'danger']
//   const index = userName.charCodeAt(0) % colors.length
//   return colors[index]
// }
</script>

<template>
  <VaCard>
    <VaCardTitle class="flex items-start justify-between">
      <h1 class="card-title text-secondary font-bold uppercase">Projects</h1>
      <VaButton preset="primary" size="small" to="/project-management/projects-overview">View all projects</VaButton>
    </VaCardTitle>
    <VaCardContent>
      <div v-if="projects.length > 0">
        <VaDataTable
          v-model:sort-by="sorting.sortBy"
          v-model:sorting-order="sorting.sortingOrder"
          :items="projects"
          :columns="columns"
          :loading="isLoading"
        >
          <template #cell(projectName)="{ rowData }">
            <div class="ellipsis max-w-[230px] lg:max-w-[450px]">
              {{ rowData.name }}
            </div>
          </template>
          <!-- <template #cell(project_owner)="{ rowData }">
            <div class="flex items-center gap-2 ellipsis max-w-[230px]">
              <UserAvatar :user="rowData.project_owner" size="small" />
              {{ rowData.project_owner.fullname }}
            </div>
          </template> -->
          <!-- <template #cell(team)="{ rowData: project }">
            <VaAvatarGroup
              size="small"
              :options="
                (project as Project).team.map((user) => ({
                  label: user.fullname,
                  src: user.avatar,
                  fallbackText: user.fullname[0],
                  color: avatarColor(user.fullname),
                }))
              "
              :max="2"
            />
          </template> -->
          <template #cell(mainType)="{ rowData: project }">
            <ProjectBadges :level="'main'" :type="project.mainType" />
          </template>
          <template #cell(subType)="{ rowData: project }">
            <ProjectBadges :level="'sub'" :type="project.subType" />
          </template>
        </VaDataTable>
      </div>
      <div v-else class="p-4 flex justify-center items-center text-[var(--va-secondary)]">No projects</div>
    </VaCardContent>
  </VaCard>
</template>
